package com.juneyaoair.oneorder.api.pay.service.impl;

import cn.hutool.extra.qrcode.QrCodeUtil;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.UnifiedOrderResultEnum;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.request.payMethod.ParamPayMethodDTO;
import com.juneyaoair.flightbasic.response.paynote.PayMethodDTO;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.exception.OrderServiceException;
import com.juneyaoair.oneorder.api.pay.config.PayConfig;
import com.juneyaoair.oneorder.api.pay.dto.*;
import com.juneyaoair.oneorder.api.pay.service.IPayService;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.constant.PatternCommon;
import com.juneyaoair.oneorder.mobile.dto.PayChannelDetail;
import com.juneyaoair.oneorder.tools.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/28 11:24
 */
@Slf4j
@Service
public class PayService extends CommonService implements IPayService {
    @Autowired
    private FlightBasicProviderClient flightBasicProviderClient;
    @Autowired
    private PayConfig payConfig;

    private final String BILL_CREATE_IP = "BillCreateIP";
    private final String PROMO_PARAMS_STR = "promo_params_str";
    private final String FFPCARDNO_STR = "FfpCardNo";
    private final String CHK_VALUE = "ChkValue";
    private final String CARD_INFO = "CardInfo";

    /**
     * <AUTHOR>
     * @Description 会员卡号
     * @Date 9:37 2024/5/27
     **/
    private static final String MEMBER_NO = "MemberNo";

    /**
     * <AUTHOR>
     * @Description 验证码
     * @Date 9:37 2024/5/27
     **/
    private static final String VERIFY_CODE = "VerifyCode";

    private static final String VerificationCode = "VerificationCode";
    private static final String VerificationCodeNum = "VerificationCodeNum";

    private static final String PaymentTransId = "PaymentTransId";


    @Override
    public BaseResultDTO<List<PayMethodDTO>> queryAllPayMethod(BaseRequestDTO<ParamPayMethodDTO> baseRequestDTO) {
        long s = System.currentTimeMillis();
        printReqLog(String.valueOf(s), "flightBasicProviderClient.searchPayMethods", baseRequestDTO);
        BaseResultDTO<List<PayMethodDTO>> baseResultDTO = flightBasicProviderClient.searchPayMethods(baseRequestDTO);
        printResultLog(String.valueOf(s), "flightBasicProviderClient.searchPayMethods", baseResultDTO, s);
        if (!WSEnum.SUCCESS.resultCode.equals(baseResultDTO.getResultCode())) {
            throw OrderServiceException.fail(baseResultDTO.getResultInfo());
        }
        return baseResultDTO;
    }

    @Override
    public PayResult doPay(PayChannelDetail payChannelDetail, LanguageEnum languageEnum, String currency, OrderPayParam orderPayParam, String ffpCardNo, HttpServletResponse response) {
        //orderType类型转换
        Map<String, OrderTypeDesc> orderTypeDescMap;
        String returnUrl;
        if (!ChannelCodeEnum.G_B2C.getChannelCode().equals(orderPayParam.getHeadChannelCode())) {
            orderTypeDescMap = payConfig.getOrderTypeDescMap();
            returnUrl = payConfig.getReturnUrl();
        } else {
            orderTypeDescMap = initOrderTypeDesc(languageEnum.name());
            returnUrl = payConfig.getReturnUrlGB2c();
        }
        if (ObjectUtils.isEmpty(orderTypeDescMap)) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR, languageEnum.name() + "请检查payment.orderType配置");
        }
        OrderTypeDesc orderTypeDesc = orderTypeDescMap.get(orderPayParam.getOrderType());
        if (ObjectUtils.isEmpty(orderTypeDesc)) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR, languageEnum.name() + "请检查payment.orderType配置中" + orderPayParam.getOrderType() + "配置");
        }
        if (StringUtils.isNotBlank(orderTypeDesc.getReturnPage())) {
            String page;
            try {
                page = URLEncoder.encode(orderTypeDesc.getReturnPage(), "UTF-8");
            } catch (UnsupportedEncodingException e) {
                page = orderTypeDesc.getReturnPage();
            }
            returnUrl = returnUrl + "&page=" + page;
        }
        //动态参数设置
        Map<String, String> dynamicParameterMap = new HashMap<>();
        dynamicParameterMap.put(BILL_CREATE_IP, orderPayParam.getIp());
        if (StringUtils.isNotBlank(payChannelDetail.getPromoParam())) {
            dynamicParameterMap.put(PROMO_PARAMS_STR, orderPayParam.getIp());
        }
        if (StringUtils.isNotBlank(ffpCardNo)) {
            dynamicParameterMap.put(FFPCARDNO_STR, ffpCardNo);
        }
        if (StringUtils.isNotBlank(orderPayParam.getRedirectUrl())) {
            dynamicParameterMap.put("redirectUrl", orderPayParam.getRedirectUrl());
        }
        if (payChannelDetail.isNeedCardInfo() && null != orderPayParam.getCardInfo()) {
            dynamicParameterMap.put(VerificationCode, orderPayParam.getCardInfo().getVerificationCode());
            dynamicParameterMap.put(VerificationCodeNum, orderPayParam.getCardInfo().getVerificationCodeNum());
            dynamicParameterMap.put(PaymentTransId, orderPayParam.getCardInfo().getPaymentTransId());
        }
        String dynamicParameters = HoAirGsonUtil.objectToJson(dynamicParameterMap);
        dynamicParameters = dynamicParameters.replaceAll("\"", "'").replaceAll("\\\\'", "\\\\\"");
        //支付渠道公共配置
        PaymentDto paymentDto = PaymentDto.builder()
                .version(payConfig.getVersion())
                .channelNo(payChannelDetail.getPayChannel())
                .currency(StringUtils.isBlank(currency) ? payConfig.getPayCurrency() : currency)
                .noticeUrl(payConfig.getNoticeUrl())
                .returnUrl(returnUrl)
                .channelOrderNo(orderPayParam.getChannelOrderNo())
                .orderNo(orderPayParam.getOrderNo())
                .channelBuyDatetime(orderPayParam.getChannelBuyDatetime())
                .channelPriInfo(orderPayParam.getChannelPriInfo())
                .dynamicParameters(dynamicParameters)
                .gatewayNo(payChannelDetail.getGateway())
                .gatewayType(payChannelDetail.getGatewayType())
                .paymentChannelNo(payChannelDetail.getPayChannel())
                .amount(orderPayParam.getAmount())
                .useScore(orderPayParam.getUseScore())
                .orderType(orderTypeDesc.getOrderType())
                .subject(orderTypeDesc.getSubject())
                .body(orderTypeDesc.getBody())
                .payTimeout("")
                .dataCollectType("Web")
                .build();
        //已知国际官网渠道不需要此参数
        if (ChannelCodeEnum.G_B2C.getChannelCode().equals(orderPayParam.getHeadChannelCode()) &&
                "WORLDPAY".equalsIgnoreCase(orderPayParam.getMethod())) {
            paymentDto.setDynamicParameters(null);
            paymentDto.setPayTimeout(null);
            paymentDto.setPaymentChannelNo(null);
        }
        Map<String, String> paramMap = paymentDto.getPayPara();
        String sha1Content = paymentDto.getPayParaSHA1Str();
        //支付卡加密
        if (StringUtils.isNotBlank(payChannelDetail.getCardInfo())) {
            // 虚拟支付
            String cardInfo = DES3Util.des3EncodeECB(payChannelDetail.getKey().substring(0, payChannelDetail.getKey().length() - 8), payChannelDetail.getCardInfo());
            paramMap.put(CARD_INFO, cardInfo);
        } else if (payChannelDetail.isNeedCardInfo()) {
            //需要传送银行卡信息
            toTakeFeasibilityAnalyze(orderPayParam);
            paramMap.put(CARD_INFO, DES3Util.des3EncodeECB(payChannelDetail.getKey().substring(0, payChannelDetail.getKey().length() - 8), toGenerateCardInfo(orderPayParam)));
        }
        //支付签名
        String chkValue = EncoderHandler.encodeBySHA1(sha1Content + payChannelDetail.getKey());
        paramMap.put(CHK_VALUE, chkValue);
        String url = payConfig.getNetPay();
        if (payChannelDetail.isAsync()) {
            url = payConfig.getNetPayAsync();
        }
        String payResultStr = HttpUtil.doPayPost(url, paramMap);
        PayResult payResult = PayResult.builder().build();
        Pattern httpPattern = Pattern.compile(PatternCommon.WEB_HTTP, Pattern.MULTILINE);
        //json格式
        if (HoAirGsonUtil.isGoodJson(payResultStr)) {
            Map<String, String> payMap = (Map<String, String>) HoAirGsonUtil.jsonToMap(payResultStr);
            payResult.setDocType("json");
            payResult.setResult(payMap);
            if ("9999".equals(payMap.get("RespCode")) || "9999".equals(payMap.get("Status"))) {
                throw MultiLangServiceException.fail("支付异常");
            }
            //url返回在json中
            String payUrl = payMap.get("NormalUrl");
            if(StringUtils.isNotBlank(payUrl)){
                createHttpUrlResult(payChannelDetail.getMode(),payUrl,payResult,response);
            }
        } else if (httpPattern.matcher(payResultStr).matches()) { //http格式
            createHttpUrlResult(payChannelDetail.getMode(),payResultStr,payResult,response);
        } else {
            Pattern pattern = Pattern.compile(PatternCommon.HTML, Pattern.MULTILINE);
            Matcher matcher = pattern.matcher(payResultStr);
            if (matcher.matches()) {
                payResult.setDocType("form");
                payResult.setResult(payResultStr);
            } else {
                throw MultiLangServiceException.fail("未知的文档格式！");
            }
        }
        return payResult;
    }

    private void createHttpUrlResult(String mode,String payUrl,PayResult payResult,HttpServletResponse response) {
        payResult.setDocType("http");
        payResult.setResult(payUrl);
        //需要处理二维码数据流
        if ("qrcode".equals(mode)) {
            createQrCode(payUrl, response);
        }
    }

    //根据语言获取订单类型描述
    private Map<String, OrderTypeDesc> initOrderTypeDesc(String language) {
        Map<String, Map<String, OrderTypeDesc>> orderTypeMap = payConfig.getOrderTypeLangDescMap();
        if (ObjectUtils.isEmpty(orderTypeMap)) {
            throw MultiLangServiceException.fail(CommonErrorCode.SYSTEM_ERROR, language + "请检查payment.orderType.lang配置");
        }
        Map<String, OrderTypeDesc> orderTypeDescMap = orderTypeMap.get(language);
        //获取不到的情况下，默认获取英文，如果没有则需要维护
        if (ObjectUtils.isEmpty(orderTypeDescMap)) {
            orderTypeDescMap = orderTypeMap.get(LanguageEnum.EN_US.name());
        }
        return orderTypeDescMap;
    }

    @Override
    @SuppressWarnings("all")
    public boolean toCatchDFTSignInfo(String ffpCardNo) {
        if (StringUtils.isEmpty(ffpCardNo)) {
            throw OrderServiceException.fail("会员卡号不可为空");
        }
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put(MEMBER_NO, ffpCardNo);
        String doneResult = HttpUtil.doPayPost(payConfig.getCatchDeFuTongSignInfo(), requestMap);
        if (!HoAirGsonUtil.isGoodJson(doneResult)) {
            throw OrderServiceException.fail("网络出错");
        }
        Map<String, String> response = (Map<String, String>) HoAirGsonUtil.jsonToMap(doneResult);
        if (StringUtils.isEmpty(response.get("Code"))) {
            throw OrderServiceException.fail("网络出错");
        }
        if ("1001".equals(response.get("Code"))) {
            return true;
        } else if ("1002".equals(response.get("Code"))) {
            return false;
        } else {
            throw OrderServiceException.fail(response.get("ErrorMessage"));
        }
    }

    @Override
    public void toIdentify(PayChannelDetail payChannelDetail, DFTSignRequest dftSignRequest, String ffpCardNo) {
        toIdentifyOrSign(payChannelDetail, dftSignRequest, ffpCardNo, false);
    }

    @Override
    public void toSign(PayChannelDetail payChannelDetail, DFTSignRequest dftSignRequest, String ffpCardNo) {
        toIdentifyOrSign(payChannelDetail, dftSignRequest, ffpCardNo, true);
    }

    @Override
    public void toReSign(PayChannelDetail payChannelDetail, String ffpCardNo) {
        String doneResult = HttpUtil.doPayPost(payConfig.getDeFuTongReSign(), toGenerateSignParams(payChannelDetail, null, ffpCardNo, false, false));
        if (!HoAirGsonUtil.isGoodJson(doneResult)) {
            throw OrderServiceException.fail("网络出错");
        }
        Map<String, String> response = (Map<String, String>) HoAirGsonUtil.jsonToMap(doneResult);
        if (StringUtils.isEmpty(response.get("Code")) || !response.get("Code").equals("1001")) {
            throw OrderServiceException.fail(response.get("ErrorMessage"));
        }
    }

    /**
     * @param payChannelDetail
     * @param dftSignRequest
     * @param ffpCardNo
     * @param isSign           是否进行签约操作
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @Description 德付通身份验证或签约
     * @Date 12:13 2024/5/27
     **/
    private void toIdentifyOrSign(PayChannelDetail payChannelDetail, DFTSignRequest dftSignRequest, String ffpCardNo, boolean isSign) {
        Map<String, String> identifyMap = toGenerateSignParams(payChannelDetail, dftSignRequest, ffpCardNo, isSign, true);
        String doneResult = HttpUtil.doPayPost(payConfig.getDeFuTongSign(), identifyMap);
        if (!HoAirGsonUtil.isGoodJson(doneResult)) {
            throw OrderServiceException.fail("网络出错");
        }
        Map<String, String> response = (Map<String, String>) HoAirGsonUtil.jsonToMap(doneResult);
        if (StringUtils.isEmpty(response.get("Code")) || !"1001".equals(response.get("Code"))) {
            throw OrderServiceException.fail(response.get("ErrorMessage"));
        }
    }

    /**
     * @param payChannelDetail
     * @param dftSignRequest
     * @param ffpCardNo
     * @param isSign
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @Description 生成德付通身份验证/签约/解约的请求参数
     * @Date 13:51 2024/5/27
     **/
    @NotNull
    private Map<String, String> toGenerateSignParams(PayChannelDetail payChannelDetail, DFTSignRequest dftSignRequest, String ffpCardNo, boolean isSign, boolean toGeneCardInfo) {
        if (isSign && null != dftSignRequest && StringUtils.isEmpty(dftSignRequest.getVerifyCode())) {
            throw OrderServiceException.fail("验证码不可为空");
        }
        //动态参数设置
        Map<String, String> dynamicParametersMap = new HashMap<>();
        dynamicParametersMap.put(FFPCARDNO_STR, ffpCardNo);
        if (null != dftSignRequest) {
            dynamicParametersMap.put(VERIFY_CODE, isSign ? dftSignRequest.getVerifyCode() : "");
        }
        String dynamicParameters = HoAirGsonUtil.objectToJson(dynamicParametersMap);
        dynamicParameters = dynamicParameters.replaceAll("\"", "'").replaceAll("\\\\'", "\\\\\"");
        //支付渠道公共配置
        IdentifyDto identifyDto = IdentifyDto.builder()
                .version(payConfig.getVersion())
                .channelNo(payChannelDetail.getPayChannel())
                .gatewayNo(payChannelDetail.getGateway())
                .dynamicParameters(dynamicParameters)
                .build();
        Map<String, String> identifyMap = identifyDto.toGeneIdentifyMap();
        identifyMap.put(CHK_VALUE, EncoderHandler.encodeBySHA1(identifyDto.toGeneIdentifyParaSHA1Str() + payChannelDetail.getEpayKey()));
        if (toGeneCardInfo) {
            identifyMap.put(CARD_INFO, DES3Util.des3EncodeECB(payChannelDetail.getEpayKey().substring(0, payChannelDetail.getEpayKey().length() - 8), toGenerateCardInformation(dftSignRequest)));
        }
        return identifyMap;
    }

    /**
     * @param orderPayParam
     * @return java.lang.String
     * <AUTHOR>
     * @Description 生成CardInfo 证件类型写死为 NI
     * @Date 11:23 2024/4/23
     **/
    private String toGenerateCardInfo(OrderPayParam orderPayParam) {
        if (null == orderPayParam || null == orderPayParam.getCardInfo()) {
            return "";
        }
        return toGeneDefaultValue(orderPayParam.getCardInfo().getCardNo()) + "|" + toGeneDefaultValue(orderPayParam.getCardInfo().getPassWord())
                + "|" + toGeneDefaultValue(toConvertExpireDate(orderPayParam.getCardInfo().getValidDate())) + "|" + toGeneDefaultValue(orderPayParam.getCardInfo().getCvv2())
                + "|" + "NI" + "|" + toGeneDefaultValue(orderPayParam.getCardInfo().getIdNo())
                + "|" + toGeneDefaultValue(orderPayParam.getCardInfo().getName()) + "|" + toGeneDefaultValue(orderPayParam.getCardInfo().getBuyerPhone());
    }

    /**
     * @param orderPayParam
     * @return void
     * <AUTHOR>
     * @Description 参数校验
     * @Date 11:06 2024/4/23
     **/
    private void toTakeFeasibilityAnalyze(OrderPayParam orderPayParam) {
        if (null == orderPayParam || null == orderPayParam.getCardInfo()) {
            return;
        }
        if (StringUtils.isEmpty(orderPayParam.getCardInfo().getCardNo())) {
            throw OrderServiceException.fail("信用卡号不可为空");
        }
        if (StringUtils.isEmpty(orderPayParam.getCardInfo().getValidDate())) {
            throw OrderServiceException.fail("银行卡有效期不可为空");
        }
        if (StringUtils.isEmpty(orderPayParam.getCardInfo().getName())) {
            throw OrderServiceException.fail("卡所属人的姓名不可为空");
        }
        if (StringUtils.isEmpty(orderPayParam.getCardInfo().getVerificationCodeNum())) {
            throw OrderServiceException.fail("验证码序号不可为空");
        }
    }

    @Override
    public VerifyCodeSendResult toSendVerifyCode(PayChannelDetail payChannelDetail, VerifyCodeSendParam verifyCodeSendParam, String ffpCardNo, HttpServletResponse response) {
        //动态参数设置
        try {
            Map<String, String> dynamicParameterMap = new HashMap<>();
            dynamicParameterMap.put(BILL_CREATE_IP, verifyCodeSendParam.getIp());
            if (StringUtils.isNotBlank(ffpCardNo)) {
                dynamicParameterMap.put(FFPCARDNO_STR, ffpCardNo);
            }
            String dynamicParameters = HoAirGsonUtil.objectToJson(dynamicParameterMap);
            dynamicParameters = dynamicParameters.replaceAll("\"", "'").replaceAll("\\\\'", "\\\\\"");

            //公共参数设置
            VerifyCodeDto verifyCodeDto = VerifyCodeDto.builder().version(payConfig.getVersion()).channelNo(payChannelDetail.getPayChannel())
                    .gatewayNo(payChannelDetail.getGateway()).amount(formatAmount(verifyCodeSendParam.getAmount()))
                    .cardNo(verifyCodeSendParam.getCardNo()).expiryDate(toConvertExpireDate(verifyCodeSendParam.getExpiryDate()))
                    .transDate(DateUtil.convertDate2Str(new Date(), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN)).dynamicParameters(dynamicParameters).gatewayType(payChannelDetail.getGatewayType()).build();
            String chkValue = EncoderHandler.encodeBySHA1(verifyCodeDto.toCatchVerifyCodeParaSHA1Str() + payChannelDetail.getEpayKey());
            Map<String, String> paramMap = verifyCodeDto.toGenerateVerifySendMap();
            paramMap.put(CHK_VALUE, chkValue);
            String payResultStr = HttpUtil.doPayPost(payConfig.getCodeSend(), paramMap);
            if (HoAirGsonUtil.isGoodJson(payResultStr)) {
                HoMotoVerifyResponse hoMotoVerifyResponse = HoAirGsonUtil.fromJson(payResultStr, HoMotoVerifyResponse.class);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(hoMotoVerifyResponse.getRespCode())) {
                    return VerifyCodeSendResult.builder().verificationCodeNum(hoMotoVerifyResponse.getVerificationCodeNum()).paymentTransId(hoMotoVerifyResponse.getPaymentTransId()).build();
                } else {
                    throw OrderServiceException.fail(hoMotoVerifyResponse.getErrorMsg());
                }
            } else {
                throw OrderServiceException.fail("验证码发送异常");
            }
        } catch (Exception exception) {
            log.error("卡号[{}]请求发送（交行信用卡）验证码时出错，错误信息为：", ffpCardNo, exception);
            throw exception;
        }
    }

    /**
     * @param amount
     * @return java.lang.String
     * <AUTHOR>
     * @Description 规整小数保持取小数点后2位
     * @Date 14:58 2024/4/19
     **/
    public static String formatAmount(String amount) {
        if (StringUtils.isEmpty(amount)) {
            return "";
        }
        BigDecimal bd = new BigDecimal(amount);
        bd = bd.setScale(2, RoundingMode.HALF_UP); // 设置小数点后两位，四舍五入
        return bd.toString();
    }

    /**
     * 根据返回结果输出二维码流
     *
     * @param payResultStr
     */
    private void createQrCode(String payResultStr, HttpServletResponse response) {
        try {
            QrCodeUtil.generate(payResultStr, 300, 300, "jpg", response.getOutputStream());
        } catch (Exception e) {
            log.error("支付二维码生成异常:", e);
            throw OrderServiceException.fail("支付二维码生成异常！");
        }
    }

    /**
     * @param oriString
     * @return java.lang.String
     * <AUTHOR>
     * @Description 设置默认值
     * @Date 13:47 2024/4/28
     **/

    private String toGeneDefaultValue(String oriString) {
        if (StringUtils.isEmpty(oriString)) {
            return "";
        }
        return oriString;
    }

    /**
     * @param oriDate
     * @return java.lang.String
     * <AUTHOR>
     * @Description 交换信用卡有效期的年月位置
     * @Date 13:33 2024/5/17
     **/
    private String toConvertExpireDate(String oriDate) {
        if (StringUtils.isEmpty(oriDate) || oriDate.length() != 4) {
            throw OrderServiceException.fail("信用卡有效期格式错误");
        }
        return oriDate.substring(2, 4) + oriDate.substring(0, 2);
    }

    /**
     * @param dftSignRequest
     * @return java.lang.String
     * <AUTHOR> 银行卡号|密码|年月(四位,注意卡上年月前后顺序)|Cvv2(银行卡后三位或四位数字)|证件类型:0|身份证号|姓名|电话号码
     * 目前只需要卡号，身份证号，姓名，手机号这4个，把样例中对应的值替换掉。
     * @Description 生成CardInfo 证件类型写死为 0
     * @Date 11:23 2024/4/23
     **/
    private String toGenerateCardInformation(DFTSignRequest dftSignRequest) {
        if (null == dftSignRequest) {
            return "";
        }
        return toGenerateDefaultValue(dftSignRequest.getCardNo()) + "|" + toGenerateDefaultValue("")
                + "|" + toGenerateDefaultValue("") + "|" + toGenerateDefaultValue("")
                + "|" + "0" + "|" + toGenerateDefaultValue(dftSignRequest.getIdNo())
                + "|" + toGenerateDefaultValue(dftSignRequest.getName()) + "|" + toGenerateDefaultValue(dftSignRequest.getPhone());
    }

    /**
     * @param oriString
     * @return java.lang.String
     * <AUTHOR>
     * @Description 设置默认值
     * @Date 13:47 2024/4/28
     **/
    private String toGenerateDefaultValue(String oriString) {
        if (StringUtils.isEmpty(oriString)) {
            return "";
        }
        return oriString;
    }
}
