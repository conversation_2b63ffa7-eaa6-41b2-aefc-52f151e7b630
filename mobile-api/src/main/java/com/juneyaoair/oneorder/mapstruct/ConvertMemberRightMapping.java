package com.juneyaoair.oneorder.mapstruct;

import com.juneyaoair.oneorder.crm.dto.common.MemberLevelDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/3/17  18:37.
 */
@Mapper
public interface ConvertMemberRightMapping {
    ConvertMemberRightMapping MAPPER = Mappers.getMapper(ConvertMemberRightMapping.class);
    List<MemberLevelDTO> toConvertMemberRights(List<com.juneyaoair.flightbasic.response.memberCenter.MemberLevelDTO> productInfoList);

}
