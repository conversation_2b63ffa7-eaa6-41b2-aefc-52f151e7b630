package com.juneyaoair.oneorder.common.request;

import com.juneyaoair.oneorder.common.enums.VerifyCodeBizTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description 基于会员信息发送验证码
 * <AUTHOR>
 * @Date 2025/07/23 18:23
 * @Version 1.0
 */
@Data
public class SendVerifyCodeParam {

    @NotNull(message = "业务类型不能为空")
    @ApiModelProperty(value = "业务类型", required = true)
    private VerifyCodeBizTypeEnum bizType;

    @NotBlank(message = "消息类型不能为空")
    @ApiModelProperty(value = "消息类型 SMS:短信 EMAIL:邮件", required = true)
    private String messageType;

}
