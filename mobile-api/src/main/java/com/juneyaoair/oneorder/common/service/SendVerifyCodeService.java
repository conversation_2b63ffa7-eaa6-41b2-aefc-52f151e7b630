package com.juneyaoair.oneorder.common.service;

import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.enums.VerifyCodeBizTypeEnum;
import com.juneyaoair.oneorder.common.request.SendVerifyCodeParam;
import com.juneyaoair.oneorder.common.response.SendVerifyCodeResult;

/**
 * @Description 基于会员信息发送验证码
 * <AUTHOR>
 * @Date 2025/07/23 18:23
 * @Version 1.0
 */
public interface SendVerifyCodeService {

    /**
     * 基于会员信息发送验证码
     *
     * @param ffpNo
     * @param sendVerifyCodeParam
     * @param language
     * @return
     */
    SendVerifyCodeResult sendVerifyCode(String ffpNo, SendVerifyCodeParam sendVerifyCodeParam, LanguageEnum language);

    /**
     * 校验验证码
     *
     * @param bizType
     * @param messageType
     * @param ffpNo
     * @param verifyCode
     */
    void checkVerifyCode(VerifyCodeBizTypeEnum bizType, String messageType, String ffpNo, String verifyCode);

}
