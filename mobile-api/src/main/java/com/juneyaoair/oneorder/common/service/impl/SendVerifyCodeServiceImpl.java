package com.juneyaoair.oneorder.common.service.impl;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Maps;
import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.service.ICaptchaService;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.email.EmailConfigEnum;
import com.juneyaoair.oneorder.api.email.service.IMailService;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.CheckLicenseFuncEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.enums.VerifyCodeBizTypeEnum;
import com.juneyaoair.oneorder.common.request.SendVerifyCodeConfig;
import com.juneyaoair.oneorder.common.request.SendVerifyCodeParam;
import com.juneyaoair.oneorder.common.response.SendVerifyCodeResult;
import com.juneyaoair.oneorder.common.service.SendVerifyCodeService;
import com.juneyaoair.oneorder.crm.dto.common.MemberContactSoaModel;
import com.juneyaoair.oneorder.crm.dto.response.PtMemberDetail;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.utils.SensitiveInfoHider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Description 基于会员信息发送验证码
 * <AUTHOR>
 * @Date 2025/07/23 18:23
 * @Version 1.0
 */
@Slf4j
@Service
public class SendVerifyCodeServiceImpl implements SendVerifyCodeService {

    @ApolloJsonValue("${sendVerifyCodeConfig:{}}")
    private Map<String, SendVerifyCodeConfig> sendVerifyCodeConfigMap;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IMemberService memberService;
    @Autowired
    private ICaptchaService captchaService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private IMailService mailService;

    @Override
    public SendVerifyCodeResult sendVerifyCode(String ffpNo, SendVerifyCodeParam sendVerifyCodeParam, LanguageEnum language) {
        VerifyCodeBizTypeEnum bizType = sendVerifyCodeParam.getBizType();
        SendVerifyCodeConfig sendVerifyCodeConfig = sendVerifyCodeConfigMap.get(bizType.name());
        if (null == sendVerifyCodeConfig) {
            throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "业务类型不存在");
        }
        // 查询会员联系方式
        PtMemberDetail ptMemberDetail = memberService.memberDetail(ChannelCodeEnum.MOBILE.getChannelCode(), ffpNo,
                new String[]{MemberDetailRequestItemsEnum.CONTACTINFO.eName});
        List<MemberContactSoaModel> contactInfoList = ptMemberDetail.getContactInfo();
        if (CollectionUtils.isEmpty(contactInfoList)) {
            throw MultiLangServiceException.fail("会员联系方式不存在");
        }
        // 联系信息
        String areaId = null;
        String phone = null;
        String email = null;
        for (MemberContactSoaModel memberContactSoaModel : contactInfoList) {
            // 手机号
            if (ContactTypeEnum.MOBILE.getCode() == memberContactSoaModel.getContactType()) {
                String crmPhone = memberContactSoaModel.getContactNumber();
                String[] phoneArr = crmPhone.split("-");
                if (phoneArr.length > 1) {
                    areaId = phoneArr[0];
                    phone = phoneArr[1];
                } else {
                    phone = crmPhone;
                }
            }
            // 邮箱
            if (ContactTypeEnum.EMAIL.getCode() == memberContactSoaModel.getContactType()) {
                email = memberContactSoaModel.getContactNumber();
            }
        }
        // 参数校验
        switch (sendVerifyCodeParam.getMessageType()) {
            case "SMS":
                if (StringUtils.isBlank(phone)) {
                    throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "手机号不存在");
                }
                break;
            case "EMAIL":
                if (StringUtils.isBlank(email)) {
                    throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "邮箱不存在");
                }
                break;
            default:
                throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "消息类型不正确");
        }
        CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(ffpNo, CheckLicenseFuncEnum.SEND_VERIFY_CODE, bizType.name(),null, "验证码发送失败，达到单日发送上限");
        // 会员计数校验 当前不增加计数
        commonService.checkDayLicense(false, ffpCheckDayLicense);
        // 生成验证码 并 设置缓存
        String sendCode = commonService.getChkCode();
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.SEND_VERIFY_CODE + bizType.name() + ":" + sendVerifyCodeParam.getMessageType() + ":" + ffpNo);
        redisUtil.set(redisKey, sendCode, 300L);
        SendVerifyCodeResult sendVerifyCodeResult = new SendVerifyCodeResult();
        switch (sendVerifyCodeParam.getMessageType()) {
            case "SMS":
                // 获取短信模板信息
                Map<String, SendVerifyCodeConfig.SmsTemplate> smsTemplateMap = sendVerifyCodeConfig.getSmsTemplateMap();
                if (null == smsTemplateMap || smsTemplateMap.isEmpty()) {
                    throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "短信模板不能为空");
                }
                SendVerifyCodeConfig.SmsTemplate smsTemplate = smsTemplateMap.get(language.name());
                if (null == smsTemplate || StringUtils.isBlank(smsTemplate.getTemplateCode())) {
                    throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "短信模板编号不能为空");
                }
                // 发送短信验证码
                Map<String, String> extras = Maps.newHashMap();
                extras.put("sendCode", sendCode);
                extras.put("function", smsTemplate.getFunction());
                boolean smsFlag = captchaService.commonSmsSend(smsTemplate.getTemplateCode(), areaId, phone, extras);
                if (!smsFlag) {
                    throw MultiLangServiceException.fail("发送失败");
                }
                sendVerifyCodeResult.setAreaId(areaId);
                sendVerifyCodeResult.setPhone(SensitiveInfoHider.hidePhone(phone));
                break;
            case "EMAIL":
                Map<String, SendVerifyCodeConfig.MailTemplate> mailTemplateMap = sendVerifyCodeConfig.getMailTemplateMap();
                if (null == mailTemplateMap || mailTemplateMap.isEmpty()) {
                    throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "邮件模板不能为空");
                }
                SendVerifyCodeConfig.MailTemplate mailTemplate = mailTemplateMap.get(language.name());
                if (null == mailTemplate || StringUtils.isAnyBlank(mailTemplate.getSubject(), mailTemplate.getContent())) {
                    throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "邮件模板内容不能为空");
                }
                // 发送邮件验证码
                String template = mailTemplate.getContent();
                template = template.replace("{{sendCode}}", sendCode);
                template = template.replace("{{function}}", mailTemplate.getFunction());
                mailService.sendEmail(email, mailTemplate.getSubject(), template, EmailConfigEnum.CRM);
                sendVerifyCodeResult.setEmail(SensitiveInfoHider.hideMail(email));
                break;
            default:
                throw MultiLangServiceException.fail(CommonErrorCode.REQUEST_VALIDATION_FAILED, "消息类型不正确");
        }
        // 增加计数
        commonService.addDayLicense(ffpCheckDayLicense);
        return sendVerifyCodeResult;
    }

    @Override
    public void checkVerifyCode(VerifyCodeBizTypeEnum bizType, String messageType, String ffpNo, String verifyCode) {
        if (null == bizType || StringUtils.isAnyBlank(messageType, ffpNo, verifyCode)) {
            log.error("参数校验不通过，bizType:{} messageType：{} ffpNo：{} verifyCode:{}", bizType, messageType, ffpNo, verifyCode);
            throw new MultiLangServiceException(CommonErrorCode.REQUEST_VALIDATION_FAILED, "验证码错误或已经失效！");
        }
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.SEND_VERIFY_CODE + bizType.name() + ":" + messageType + ":" + ffpNo);
        String redisVerifyCode = redisUtil.getStr(redisKey);
        boolean flag = verifyCode.equals(redisVerifyCode);
        if (!flag) {
            log.error("验证码校验不通过，key:{} 验证码：{} 缓存验证码：{}", redisKey, verifyCode, redisVerifyCode);
            throw new MultiLangServiceException(CommonErrorCode.VERIFY_CODE_FAIL, "验证码错误或已经失效！");
        }
        redisUtil.del(redisKey);
    }

}
