package com.juneyaoair.oneorder.common.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 发送短信验证码配置文件
 * <AUTHOR>
 */
@Data
public class SendVerifyCodeConfig {

    @ApiModelProperty(value = "短信模板信息 key:语言 value:模板信息")
    private final Map<String,  SmsTemplate> smsTemplateMap;

    @ApiModelProperty(value = "邮件模板信息 key:语言 value:模板信息")
    private final Map<String,  MailTemplate> mailTemplateMap;

    @ApiModelProperty(value = "业务描述")
    private final String desc;

    @Data
    public static class SmsTemplate {

        @ApiModelProperty(value = "模板编号")
        private String templateCode;

        @ApiModelProperty(value = "功能描述")
        private String function;

    }

    @Data
    public static class MailTemplate {

        @ApiModelProperty(value = "邮件主题")
        private String subject;

        @ApiModelProperty(value = "邮件内容")
        private String content;

        @ApiModelProperty(value = "功能描述")
        private String function;

    }

}
