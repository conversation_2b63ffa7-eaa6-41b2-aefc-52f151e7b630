package com.juneyaoair.oneorder.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.oneorder.tools.utils.HttpResult;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class HttpSendUtil {

    /**
     * java 接口调用
     * 自定义请求时间 自定义头部参数
     *
     * @param req
     * @param requestUrl
     * @return HttpResult  返回服务器状态码
     */
    public static<T> T doPostClient(Object req, String requestUrl, TypeReference<T> typeReference) {
        HttpResult httpResult = HttpUtil.doPostClient(req, requestUrl);
        if (!httpResult.isResult()) {
            throw MultiLangServiceException.fail("调用远程服务繁忙");
        }
        String result = httpResult.getResponse();
        if (StringUtils.isBlank(result)) {
            throw MultiLangServiceException.fail("调用远程服务异常");
        }
        // 解析数据
        return JSON.parseObject(result, typeReference);
    }

}
