package com.juneyaoair.oneorder.util;

import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.mobile.exception.MultiLangServiceException;

/**
 * <AUTHOR>
 */
public class ResultUtils {

    /**
     * 解析结果数据
     * @param baseResult
     * @return
     * @param <T>
     */
    public static <T> T getFlightBasicResult(BaseResultDTO<T> baseResult){
        if (null == baseResult) {
            throw new MultiLangServiceException("数据获取失败！");
        }
        if (WSEnum.SUCCESS.resultCode.equals(baseResult.getResultCode())) {
            return baseResult.getResult();
        }
        throw new MultiLangServiceException(baseResult.getResultInfo());
    }

}
