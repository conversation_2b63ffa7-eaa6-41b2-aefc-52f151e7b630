package com.juneyaoair.oneorder.tools.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class EncoderHandlerTest {

    @Test
    void encode() {
        String encode = EncoderHandler
                .encodeBySHA1("10B2C79605N2023102741083174705DORD2310276295051120231027091128CNY1955.00https://newb2cuat.juneyaoair.comhttp://127.0.0.1{'BillCreateIP':'**************'}机票机票订单6WebB2C2ec04d0e7^dbe&5cc*b377#4832&6842");
        System.out.println("__________");
        System.out.println(encode);
        System.out.println("9F33A96B227D4F3D8AA081B2AEDD5546D90140FF".equals(encode));
        Assertions.assertEquals("9F33A96B227D4F3D8AA081B2AEDD5546D90140FF",encode);
    }
}