package com.juneyaoair.oneorder.tools.utils;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/30 17:59
 */
@Slf4j
public class RSAUtilTest {
    @Test
    public void rsaTest() throws Exception {
        // 1. 生成密钥对
        Map<String, String> keyPair = RSAUtil.generateKeyPair();
        String publicKey = keyPair.get("publicKey");
        String privateKey = keyPair.get("privateKey");

        System.out.println("公钥:\n" + publicKey);
        System.out.println("私钥:\n" + privateKey);

        // 2. 加密解密测试
        String originalText = "Hello, RSA加密测试!";
        System.out.println("\n原始数据: " + originalText);

        String encryptedText = RSAUtil.encrypt(originalText, publicKey);
        System.out.println("加密后: " + encryptedText);

        String decryptedText = RSAUtil.decrypt(encryptedText, privateKey);
        System.out.println("解密后: " + decryptedText);

        // 3. 签名验签测试
        String dataToSign = "这是一条需要签名的数据";
        System.out.println("\n待签名数据: " + dataToSign);

        String signature = RSAUtil.sign(dataToSign, privateKey);
        System.out.println("签名结果: " + signature);

        boolean isValid = RSAUtil.verify(dataToSign, signature, publicKey);
        System.out.println("验签结果: " + isValid);

        // 4. 测试篡改数据后的验签
        String tamperedData = "这是一条被篡改的数据";
        boolean isTamperedValid = RSAUtil.verify(tamperedData, signature, publicKey);
        System.out.println("篡改数据后验签: " + isTamperedValid);
    }

    @Test
    public void generateKeyPair() throws Exception {
        Map<String, String> keyPair = RSAUtil.generateKeyPair();
        String publicKey = keyPair.get("publicKey");
        String privateKey = keyPair.get("privateKey");
        System.out.println("公钥:\n" + publicKey);
        System.out.println("私钥:\n" + privateKey);
    }
}