package com.juneyaoair.oneorder.tools.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

/**
 * spring mvc内置的json转换工具
 * 内部实现了对象的单例
 * <AUTHOR>
 * @since 2023-06-26
 */
@Slf4j
public class JacksonUtil {
	private static ObjectMapper objectMapper = null;

	static {
		if (objectMapper == null) {
			objectMapper = new ObjectMapper();
			//序列化不为空的值
			objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
			//遇到未知属性不报错  设置为false，表示：json中字段多了，不会影响json转Object
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			/*解析json时，过滤JSON注释符*/
			objectMapper.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
			/*解析JSON时，可以解析属性值为单引号的属性名*/
			objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
			/*解析JSON时，可以解析 属性名没有双引号的非标准json字符串*/
			objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
		}
	}
	/**
	 * 转成json序列，并把结果输出成字符串
	 * 
	 * @param obj
	 * @return
	 */
	public static String objectToJson(Object obj) {
		String jsonStr = null;
		if (objectMapper != null) {
			try {
				jsonStr = objectMapper.writeValueAsString(obj);
			} catch (JsonProcessingException e) {
				log.error("json转换异常:",e);
			}
		}
		return jsonStr;
	}

	/**
	 * object对象转为Dto
	 * @param obj
	 * @param typeReference
	 * @return
	 * @param <T>
	 */
	public static <T> T objectToDto(Object obj, TypeReference<T> typeReference){
		if (objectMapper != null) {
			return objectMapper.convertValue(obj,typeReference);
		}
		return null;
	}
}