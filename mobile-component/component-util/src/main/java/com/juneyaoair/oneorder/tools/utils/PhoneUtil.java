package com.juneyaoair.oneorder.tools.utils;

import com.juneyaoair.oneorder.tools.utils.dto.CrmPhoneInfo;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description
 * @date 2018/11/7  15:30.
 */
public class PhoneUtil {
    private static String _86 = "86";

    /**
     * 手机号国际化处理 86的不会有前缀
     * @param countryCode
     * @param mobileNum
     * @return
     */
    public static String formatMobile(String countryCode, String mobileNum) {
        if (checkInternational(countryCode)) {
            return countryCode + "-" + mobileNum;
        } else {
            return mobileNum;
        }
    }

    /**
     * 手机号格式处理 主要是去86前缀
     * @param mobileNum 886-123456789
     * @return 886-123456789
     */
    public static String removeMobile86(String mobileNum){
        String[] splits = mobileNum.split("-");
        if(splits.length == 1){
            return mobileNum;
        }
        //86开头的手机号
        if(_86.equals(splits[0])){
            return splits[1];
        }
        return mobileNum;
    }

    /**
     * 国际国内判断
     *
     * @param countryCode
     * @return false  国内  true 国际/地区
     */
    public static boolean checkInternational(String countryCode) {
        if (StringUtils.isBlank(countryCode)) {
            return false;
        } else {
            if (_86.equals(countryCode.trim())) {
                return false;
            } else {
                return true;
            }
        }
    }

    /**
     * @description 解析带有-手机号的格式，规范解析手机号
     * <AUTHOR>
     * @date 2024/11/8 13:52
     * @param crmPhone
     * @return CrmPhoneInfo
     **/
    public static CrmPhoneInfo formatCrmPhoneInfo(String crmPhone){
        CrmPhoneInfo crmPhoneInfo = new CrmPhoneInfo();
        crmPhoneInfo.setCrmPhone(crmPhone);
        // 发送短信
        String[] split = crmPhone.split("-");
        String areaId = null;
        String phone;
        if (split.length > 1) {
            areaId = split[0];
            phone = split[1];
        } else {
            phone = split[0];
        }
        crmPhoneInfo.setAreaId(areaId);
        crmPhoneInfo.setPhone(phone);
        return crmPhoneInfo;
    }
}
