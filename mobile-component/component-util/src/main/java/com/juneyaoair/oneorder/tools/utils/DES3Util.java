package com.juneyaoair.oneorder.tools.utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import java.security.Key;

public class DES3Util {
    public static String des3EncodeECB(String key, String data)  
    {  
    	try
    	{
            Key deskey = null;  
            DESedeKeySpec spec = new DESedeKeySpec(getKey(key));  
            SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");  
            deskey = keyfactory.generateSecret(spec);  
            Cipher cipher = Cipher.getInstance(new StringBuilder("desede/ECB/PKCS5Padding").toString());
            cipher.init(Cipher.ENCRYPT_MODE, deskey);  
            byte[] bOut = cipher.doFinal(data.getBytes("UTF-8"));  
            return byte2hex(bOut);  	
    	}
    	catch(Exception e)
    	{
    		System.out.println(e.toString());
    		return null;
    	}
    }
    public static String des3DecodeECB(String key,String data)  
    {
    	try
    	{
    		Key deskey = null;  
    		DESedeKeySpec spec = new DESedeKeySpec(getKey(key));  
    		SecretKeyFactory keyfactory = SecretKeyFactory.getInstance("desede");
    		deskey = keyfactory.generateSecret(spec);  
    		Cipher cipher = Cipher.getInstance(new StringBuilder("desede/ECB/PKCS5Padding").toString());
    		cipher.init(Cipher.DECRYPT_MODE, deskey);  
    		byte[] bOut = cipher.doFinal(hex2byte(data));  
    		return new String(bOut, "UTF-8");  
    	}
    	catch(Exception e)
    	{
    		System.out.println(e.toString());
    		return null;
    	}
    }
    private static String byte2hex(byte bytes[]){
      StringBuffer retString = new StringBuffer();
      for (int i = 0; i < bytes.length; ++i)
      {
        retString.append(Integer.toHexString(0x0100 + (bytes[i] & 0x00FF)).substring(1).toUpperCase());
      }
      return retString.toString();
    }

    private static byte[] hex2byte(String hex) {
      byte[] bts = new byte[hex.length() / 2];
      for (int i = 0; i < bts.length; i++) {
         bts[i] = (byte) Integer.parseInt(hex.substring(2*i, 2*i+2), 16);
      }
      return bts; 
    }  

    private static byte[] getKey(String KeyString) throws Exception
    {

        return KeyString.substring(0,24).getBytes("UTF-8");
    }
}
