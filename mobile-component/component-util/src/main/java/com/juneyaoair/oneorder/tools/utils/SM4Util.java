package com.juneyaoair.oneorder.tools.utils;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Provider;
import java.security.Security;

/**
 * Title: Sm4Util <br/>
 * Description: 国密工具
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2020年4月1日15:16:03
 */
public class SM4Util {

    private static final Provider PROVIDER = new BouncyCastleProvider();

    static {
        Security.addProvider(new BouncyCastleProvider());
    }

    private static final String ENCODING = StandardCharsets.UTF_8.name();
    public static final String ALGORITHM_NAME = "SM4";
    /** 加密算法/分组加密模式/分组填充方式
      * 定义分组加密模式使用：NoPadding
    */
    public static final String ALGORITHM_NAME_ECB_PADDING = "SM4/ECB/PKCS5Padding";

    /**
     * 生成ECB暗号
     *
     * @param algorithmName 算法名称
     * @param mode          模式
     * @param key
     * @return
     * @throws Exception
     * @explain ECB模式（电子密码本模式：Electronic codebook）
     */
    private static Cipher generateEcbCipher(String algorithmName, int mode, byte[] key) throws Exception {
        Cipher cipher = Cipher.getInstance(algorithmName, PROVIDER);
        SecretKeySpec sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);
        cipher.init(mode, sm4Key);
        return cipher;
    }

    /**
     * sm4加密(返回hex加密数据)
     *
     * @param key      16进制密钥（忽略大小写）
     * @param paramStr 待加密字符串
     * @return 返回16进制的加密字符串
     * @throws Exception
     * @explain 加密模式：ECB
     * 密文长度不固定，会随着被加密字符串长度的变化而变化
     */
    public static String encryptEcb(String paramStr, String key) throws Exception {
        // 加密后的数组
        byte[] cipherArray = encryptEcb(key.getBytes(StandardCharsets.UTF_8), paramStr.getBytes(ENCODING));
        // byte[]-->hexString
        return ByteUtil.byte2hex(cipherArray).toUpperCase();
    }

    /**
     * sm4加密（返回base64加密数据）
     *
     * @param key      16进制密钥（忽略大小写）
     * @param paramStr 待加密字符串
     * @return 返回16进制的加密字符串
     * @throws Exception
     * @explain 加密模式：ECB
     * 密文长度不固定，会随着被加密字符串长度的变化而变化
     */
    public static String encryptEcb2Base64(String paramStr, String key) throws Exception {
        // 加密后的数组
        byte[] cipherArray = encryptEcb(key.getBytes(StandardCharsets.UTF_8), paramStr.getBytes(ENCODING));
        // byte[]-->hexString
        return new String(Base64.encode(cipherArray));
    }

    /**
     * 加密模式之Ecb（返回byte数组）
     *
     * @param key
     * @param data
     * @return
     * @throws Exception
     * @explain
     */
    public static byte[] encryptEcb(byte[] key, byte[] data) throws Exception {
        Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.ENCRYPT_MODE, key);
        return cipher.doFinal(data);
    }

    /**
     * sm4解密（base64）
     *
     * @param key        16进制密钥
     * @param cipherText 16进制的加密字符串（忽略大小写）
     * @return 解密后的字符串
     * @throws Exception
     * @explain 解密模式：采用ECB
     */
    public static String base64decryptEcb(String cipherText, String key) throws Exception {
        // 解密
        byte[] srcData = decryptEcb(key.getBytes(StandardCharsets.UTF_8), Base64.decode(cipherText));
        // byte[]-->String
        return new String(srcData, ENCODING);
    }


    /**
     * sm4解密(hex)
     *
     * @param cipherText 16进制的加密字符串（忽略大小写）
     * @param key        16进制密钥
     * @return 解密后的字符串
     * @throws Exception
     * @explain 解密模式：采用ECB
     */
    public static String decryptEcb(String cipherText, String key) throws Exception {
        // 解密
        byte[] srcData = decryptEcb(key.getBytes(StandardCharsets.UTF_8), ByteUtil.hex2byte(cipherText.toUpperCase()));
        // byte[]-->String
        return new String(srcData, ENCODING);
    }


    /**
     * 解密
     *
     * @param key
     * @param cipherText
     * @return
     * @throws Exception
     * @explain
     */
    public static byte[] decryptEcb(byte[] key, byte[] cipherText) throws Exception {
        Cipher cipher = generateEcbCipher(ALGORITHM_NAME_ECB_PADDING, Cipher.DECRYPT_MODE, key);
        return cipher.doFinal(cipherText);
    }


}
