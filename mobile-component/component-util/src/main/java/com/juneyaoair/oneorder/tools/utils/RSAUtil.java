package com.juneyaoair.oneorder.tools.utils;

import javax.crypto.Cipher;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * RSA工具类
 */
public class RSAUtil {
    // 加密算法名称
    private static final String ALGORITHM = "RSA";
    // 签名算法
    private static final String SIGNATURE_ALGORITHM = "SHA256withRSA";
    // 密钥长度
    private static final int KEY_SIZE = 2048;
    /**
     * 生成RSA密钥对
     * @return 包含公钥和私钥的Map
     */
    public static Map<String, String> generateKeyPair() throws Exception {
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance(ALGORITHM);
        keyPairGen.initialize(KEY_SIZE);
        KeyPair keyPair = keyPairGen.generateKeyPair();

        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();

        Map<String, String> keyMap = new HashMap<>();
        keyMap.put("publicKey", Base64.getEncoder().encodeToString(publicKey.getEncoded()));
        keyMap.put("privateKey", Base64.getEncoder().encodeToString(privateKey.getEncoded()));

        return keyMap;
    }

    /**
     * RSA公钥加密
     * @param data 待加密数据
     * @param publicKey 公钥(Base64编码)
     * @return 加密后的Base64编码字符串
     */
    public static String encrypt(String data, String publicKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        PublicKey pubKey = keyFactory.generatePublic(keySpec);

        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        byte[] encryptedData = cipher.doFinal(data.getBytes());

        return Base64.getEncoder().encodeToString(encryptedData);
    }

    /**
     * RSA私钥解密
     * @param encryptedData 加密数据(Base64编码)
     * @param privateKey 私钥(Base64编码)
     * @return 解密后的字符串
     */
    public static String decrypt(String encryptedData, String privateKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        PrivateKey priKey = keyFactory.generatePrivate(keySpec);

        Cipher cipher = Cipher.getInstance(ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        byte[] decodedData = Base64.getDecoder().decode(encryptedData);
        byte[] decryptedData = cipher.doFinal(decodedData);

        return new String(decryptedData);
    }

    /**
     * RSA私钥签名
     * @param data 待签名数据
     * @param privateKey 私钥(Base64编码)
     * @return 签名(Base64编码)
     */
    public static String sign(String data, String privateKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        PrivateKey priKey = keyFactory.generatePrivate(keySpec);

        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(priKey);
        signature.update(data.getBytes());

        byte[] signBytes = signature.sign();
        return Base64.getEncoder().encodeToString(signBytes);
    }

    /**
     * RSA公钥验签
     * @param data 原始数据
     * @param sign 签名(Base64编码)
     * @param publicKey 公钥(Base64编码)
     * @return 验签结果
     */
    public static boolean verify(String data, String sign, String publicKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        PublicKey pubKey = keyFactory.generatePublic(keySpec);

        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(pubKey);
        signature.update(data.getBytes());

        byte[] signBytes = Base64.getDecoder().decode(sign);
        return signature.verify(signBytes);
    }
}
