package com.juneyaoair.oneorder.tools.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

public class FileUtils {
    private FileUtils(){}
    private static Logger log = LoggerFactory.getLogger(FileUtils.class);

    /**
     * 获取图片名
     *
     * @param fileName
     * @return
     */
    public static String getFileName(String fileName) {
        String returnName = fileName;
        if (fileName.contains(".")) {
            returnName = fileName.substring(0, fileName.lastIndexOf("."));
        } else if(fileName == null || fileName.length() == 0) {
            returnName = UUID.randomUUID().toString();
        }
        return returnName;
    }

    private static String readFile(String path){
        StringBuilder laststr = new StringBuilder();
        try(InputStream inputStream = FileUtils.class.getResourceAsStream(path);
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, StandardCharsets.UTF_8);
            BufferedReader reader = new BufferedReader(inputStreamReader);){

            String tempString;
            while((tempString = reader.readLine()) != null){
                laststr.append(tempString);
            }
        }catch(IOException e){
            log.error(e.getMessage(), e);
        }
        return laststr.toString();
    }

    public static String readJson(String path){
        return FileUtils.readFile(path);
    }

}
