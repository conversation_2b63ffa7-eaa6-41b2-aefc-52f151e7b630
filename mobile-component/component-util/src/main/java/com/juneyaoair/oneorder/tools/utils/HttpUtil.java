package com.juneyaoair.oneorder.tools.utils;

import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * http调用
 */
public class HttpUtil {
    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);
    private static final String SERVICE_CHARSET = "utf-8";
    private static final int connectTimeout = 5000;
    private static final int readTimeout = 20000;

    /**
     * 获取自定义的Map头部信息
     *
     * @param clientIp 用户真实的IP
     * @param type     不同的类型设置不同的头部信息
     * @return
     */
    public static Map<String, String> getHeaderMap(String clientIp, String type) {
        return getHeaderMap(clientIp, null, type);
    }

    /**
     * 获取自定义的Map头部信息
     *
     * @param clientIp 用户真实的IP
     * @param type     不同的类型设置不同的头部信息 默认为空
     * @return
     */
    public static Map<String, String> getHeaderMap(String clientIp, Map<String, String> paramMap, String type) {
        Map<String, String> headMap = new HashMap<>();
        if (paramMap != null && !paramMap.isEmpty()) {
            headMap.putAll(paramMap);
        }
        //默认的配置选项
        headMap.put("X-HO-Client-Host", clientIp);
        return headMap;
    }

    /**
     * java 接口调用
     * 自定义请求时间 自定义头部参数
     *
     * @param req
     * @param requestUrl
     * @return HttpResult  返回服务器状态码
     */
    public static HttpResult doPostClient(Object req, String requestUrl) {
        return doPostClient(req, requestUrl, null, readTimeout, connectTimeout);
    }

    /**
     * java 接口调用
     * 自定义请求时间 自定义头部参数
     *
     * @param req
     * @param requestUrl
     * @return HttpResult  返回服务器状态码
     */
    public static HttpResult doPostClient(Object req, String requestUrl, Map<String, String> headMap) {
        return doPostClient(req, requestUrl, headMap, readTimeout, connectTimeout);
    }

    /**
     * java 接口调用
     * 自定义请求时间 自定义头部参数
     *
     * @param req
     * @param requestUrl
     * @param readTime
     * @param connectTime
     * @return HttpResult  返回服务器状态码
     */
    public static HttpResult doPostClient(Object req, String requestUrl, int readTime, int connectTime) {
        return doPostClient(req, requestUrl, null, readTime, connectTime);
    }

    /**
     * java 接口调用
     * 自定义请求时间 自定义头部参数
     *
     * @param req
     * @param requestUrl
     * @param headMap
     * @param readTimeout
     * @param connectTimeout
     * @return HttpResult  返回服务器状态码
     */
    public static HttpResult doPostClient(Object req, String requestUrl, Map<String, String> headMap, int readTimeout, int connectTimeout) {
        return doPostClient(req, requestUrl, headMap, readTimeout, connectTimeout, null);
    }

    /**
     * @param req
     * @param requestUrl
     * @param headMap
     * @param readTimeout
     * @param connectTimeout
     * @param httpHost       服务代理设置
     * @return
     */
    public static HttpResult doPostClient(Object req, String requestUrl, Map<String, String> headMap, int readTimeout, int connectTimeout, HttpHost httpHost) {
        HttpResult result = new HttpResult();
        if (null == req) {
            result.setResult(false);
            result.setResponse("请求参数不能为空");
            return result;
        }
        String param = HoAirGsonUtil.objectToJson(req);
        long t1 = System.currentTimeMillis();
        log.info("时间戳：{},请求的地址：{},请求的参数：{}", t1, requestUrl, param);
        RequestConfig requestConfig;
        if (httpHost != null) {
            requestConfig = RequestConfig.custom().setSocketTimeout(readTimeout).setConnectTimeout
                    (connectTimeout).setConnectionRequestTimeout(connectTimeout).setProxy(httpHost).build();
        } else {
            requestConfig = RequestConfig.custom().setSocketTimeout(readTimeout).setConnectTimeout
                    (connectTimeout).setConnectionRequestTimeout(connectTimeout).build();
        }
        HttpPost httpPost = new HttpPost(requestUrl);
        httpPost.setConfig(requestConfig);
        StringEntity s = new StringEntity(param, SERVICE_CHARSET);
        httpPost.setEntity(s);
        httpPost.setHeader("Content-Type", "application/json;charset=utf-8");
        if (headMap != null && !headMap.isEmpty()) {
            for (Map.Entry<String, String> entry : headMap.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        result = sendRequest(httpPost, SERVICE_CHARSET);
        log.info("时间戳：{},请求的地址：{},响应耗时：{}ms,返回的结果：{}", t1, requestUrl, System.currentTimeMillis() - t1, result.getResponse());
        return result;
    }

    private static HttpResult sendRequest(HttpRequestBase httpRequestBase, String encode) {
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpResult httpResult = new HttpResult();
        HttpEntity entity = null;
        CloseableHttpResponse response = null;
        String responseString = null;
        try {
            response = httpclient.execute(httpRequestBase);
            log.info("返回状态：{}", response.getStatusLine());
            if (HttpStatus.SC_OK != response.getStatusLine().getStatusCode()) {
                httpResult.setResult(false);
                httpResult.setResponse("服务器内部错误(" + response.getStatusLine().getStatusCode() + ")");
                httpResult.setServerCode("" + response.getStatusLine().getStatusCode());
                return httpResult;
            } else {
                httpResult.setResult(true);
                entity = response.getEntity();
                if (entity != null) {
                    responseString = EntityUtils.toString(entity, encode);
                } else {
                    responseString = "";
                }
                httpResult.setResponse(responseString);
                httpResult.setServerCode("" + response.getStatusLine().getStatusCode());
                return httpResult;
            }
        } catch (SocketTimeoutException e) {
            httpResult.setResult(false);
            httpResult.setResponse("服务器超时)");
            if (null != response) {
                httpResult.setServerCode("" + response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            httpResult.setResult(false);
            httpResult.setResponse("服务器错误");
            if (null != response) {
                httpResult.setServerCode("" + response.getStatusLine().getStatusCode());
            }
        } finally {
            //关闭对应的连接资源
            if (response != null) {
                try {
                    response.close();
                } catch (IOException e) {
                    log.error("关闭CloseableHttpResponse发生异常: ", e);
                }
            }
            httpRequestBase.releaseConnection();
            if (httpclient != null) {
                try {
                    httpclient.close();
                } catch (IOException e) {
                    log.error("关闭CloseableHttpClient发生异常: ", e);
                }

            }
        }
        return httpResult;
    }

    /**
     * 支付请求
     *
     * @param url
     * @return
     */
    public static String doPayPost(String url, Map<String, String> map) {
        long startTime = System.currentTimeMillis();
        String result = "";
        long t1 = System.currentTimeMillis();
        try {
            log.info("时间戳：{},请求的地址：{},请求的参数：{}", t1, url, HoAirGsonUtil.objectToJson(map));
            CloseableHttpClient httpclient = HttpClientBuilder.create().build();
            HttpPost post = new HttpPost(url);
            RequestConfig.Builder requestBuilder = RequestConfig.custom()
                    .setConnectTimeout(0)
                    .setConnectionRequestTimeout(90000)
                    .setSocketTimeout(90000);
            post.setConfig(requestBuilder.build());

            //获取参数
            List<NameValuePair> nvps = new ArrayList<NameValuePair>();
            if (map != null) {
                for (Map.Entry<String, String> entry : map.entrySet()) {
                    nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
            }
            HttpEntity httpEntity = new UrlEncodedFormEntity(nvps, "UTF-8");
            post.setEntity(httpEntity);
            // 访问支付接口
            CloseableHttpResponse response = httpclient.execute(post);
            HttpEntity entityResult = response.getEntity();
            if (entityResult != null) {
                BufferedReader bufferedReader = new BufferedReader(
                        new InputStreamReader(entityResult.getContent(), "UTF-8"), 8 * 1024);
                result = bufferedReader.readLine();
                long useTime = DateUtil.useTime(startTime);
                log.info("时间戳：{},请求的地址：{},响应耗时：{}ms,返回的结果：{}", t1, url, useTime, result);
            }
        } catch (Exception e) {
            long useTime = DateUtil.useTime(startTime);
            log.info("时间戳：{},请求的地址：{},响应耗时：{}ms,异常信息:", t1, url, useTime, result, e);
        }
        return result;
    }

    public static String urlEncode(String value) {
        try {
            return URLEncoder.encode(value, SERVICE_CHARSET);
        } catch (UnsupportedEncodingException e) {
            log.error("不支持的编码:", e);
            throw new RuntimeException(e);
        }
    }
}
