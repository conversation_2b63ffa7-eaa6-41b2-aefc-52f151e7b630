package com.juneyaoair.oneorder.tools.utils;


import org.apache.commons.lang3.StringUtils;

import java.util.Date;

public class RedisKeyUtil {

    /**
     * 会员基础地址信息
     */
    public static final String COMMON_CRM_ADDRESS = "common:crmBaseAddress_v2";

    private static final String FOREVER = "forever_";
    private static final String DATE_FORMAT = "yyyy-MM-dd";

    public static String addPrefix(String str){
        return "Bff:" + str;
    }
    //获取访问控制永久key
    public static String getDayVisitForeverLimitKey(
            String dir,
            String ip,
            String source,
            String type) {
        return (StringUtils.isNotEmpty(dir) ? (dir + ":") : "")
                + FOREVER + ip + "_" + source + type;
    }

    //获取访问控制当日key
    public static String getDayVisitKey(
            String dir,
            String ip,
            String source,
            String type) {
        return (StringUtils.isNotBlank(dir) ? (dir + ":") : "")
                +
                DateUtil.dateToString(new Date(), DATE_FORMAT)
                + ip + "_" + source + type;
    }

    public static String getSmsDayVisitMinLimitKey(String source,
                                                   String limitType, String type) {
        return String.format("%s:%s:%s_min:%s", source, DateUtil.dateToString(new Date(), DATE_FORMAT), limitType, type);
    }

    public static String getSmsDayVisitKey(String source, String limitType, String type) {//删除了旧代码的环境
        return String.format("%s:%s:%s:%s", source, DateUtil.dateToString(new Date(), DATE_FORMAT), limitType, type);
    }

    public static String getSmsForeverVisitKey(String source, String limitType, String type) {
        return String.format("%s:%s:%s_forever:%s", source, DateUtil.dateToString(new Date(), DATE_FORMAT), limitType, type);
    }

    public static String getWXQRCode(String sceneMD5) {
        return addPrefix(String.format("sceneMD5:%s", sceneMD5));
    }



}
