package com.juneyaoair.oneorder.tools.utils;

import org.apache.commons.lang3.EnumUtils; /**
 * <AUTHOR>
 * @description
 * @date 2024/12/25 21:04
 */
public class HoEnumUtil extends EnumUtils {


    /**
     * 根据枚举ToString方法验证数据正确性
     *
     * @param enumClass
     * @param stringValue
     * @return
     */
    public static boolean isValidEnumByString(Class enumClass, String stringValue) {
        Enum[] arr = (Enum[]) enumClass.getEnumConstants();
        int len = arr.length;
        for (int i = 0; i < len; ++i) {
            Enum e = arr[i];
            if (e.toString().equals(stringValue)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据枚举ToString方法返回结果
     *
     * @param enumClass
     * @param stringValue
     * @return
     */
    public static Enum getEnumByString(Class enumClass, String stringValue) {
        Enum[] arr = (Enum[]) enumClass.getEnumConstants();
        int len = arr.length;
        for (int i = 0; i < len; ++i) {
            Enum e = arr[i];
            if (e.toString().equals(stringValue)) {
                return e;
            }
        }
        return null;
    }

    public static Enum getEnumByCode(Class enumClass, int code) {
        Enum[] arr = (Enum[]) enumClass.getEnumConstants();
        int len = arr.length;
        for (int i = 0; i < len; ++i) {
            Enum e = arr[i];
            if (e.ordinal()==code) {
                return e;
            }
        }
        return null;
    }
}
