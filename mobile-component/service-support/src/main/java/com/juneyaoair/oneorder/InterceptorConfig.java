package com.juneyaoair.oneorder;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        HOContextInterceptor baseInterceptor = new HOContextInterceptor();
        List<String> patterns = new ArrayList<>();
        patterns.add("/helloworld");
        registry.addInterceptor(baseInterceptor).addPathPatterns("/**").excludePathPatterns(patterns);
    }
}
