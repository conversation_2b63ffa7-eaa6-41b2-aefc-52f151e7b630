package com.juneyaoair.oneorder;

import brave.Tracing;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.exception.OrderServiceException;
import com.juneyaoair.oneorder.common.Executor;
import com.juneyaoair.oneorder.common.concurrency.HOContext;
import com.juneyaoair.oneorder.common.request.RequestHead;
import com.juneyaoair.oneorder.common.util.SpringContextUtil;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.order.util.JacksonUtil;
import com.juneyaoair.oneorder.tools.utils.HoAirGsonUtil;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.RequestFacade;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * ContentType为json 读取请求体到HOContext
 */
@Component
@WebFilter(filterName = "HOContextSetFilter", urlPatterns = "/*")
@Slf4j
public class HOContextSetFilter implements Filter {

    //下载文件等要对response流处理的uri放于此处
    private static final List<String> notDealResponseUrls = CollUtil.newArrayList("orderPay", "redirectHomePage");

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
        if ("/FilesUpload".equals(((RequestFacade) servletRequest).getServletPath()) || "/DownloadFile".equals(((RequestFacade) servletRequest).getServletPath())
                || "/airtrans/OrderReceive".equals(((RequestFacade) servletRequest).getServletPath())) {
            chain.doFilter(servletRequest, servletResponse);
            return;
        }
        if (servletRequest.getContentType() != null && (
                servletRequest.getContentType().contains(MediaType.APPLICATION_JSON_VALUE))) {
            BRHttpServletRequestWrapper requestWrapper = new BRHttpServletRequestWrapper((HttpServletRequest) servletRequest);
            boolean success = initContext(requestWrapper, (HttpServletResponse) servletResponse);
            if (!success) {
                return;
            }
            if (isNeedDealResponseFlag(servletRequest)) {
                ResponseWrapper responseWrapper = new ResponseWrapper((HttpServletResponse) servletResponse);
                chain.doFilter(requestWrapper, responseWrapper);
                writeResponse((HttpServletResponse) servletResponse, responseWrapper);
                return;
            }
            chain.doFilter(requestWrapper, servletResponse);
            return;
        }
        chain.doFilter(servletRequest, servletResponse);
    }

    private static boolean isNeedDealResponseFlag(ServletRequest servletRequest) {
        if (!(servletRequest instanceof RequestFacade)) {
            return true;
        }
        String servletPath = ((RequestFacade) servletRequest).getServletPath();
        if (StrUtil.isBlank(servletPath)) {
            return true;
        }
        return CollUtil.isEmpty(notDealResponseUrls) || notDealResponseUrls.stream().noneMatch(i -> servletPath.contains(i));
    }

    private static void writeResponse(HttpServletResponse servletResponse, ResponseWrapper responseWrapper) {
        if (StringUtils.isNotBlank(responseWrapper.getContentType())
                && (responseWrapper.getContentType().contains(MediaType.APPLICATION_JSON_VALUE))) {
            try {
                Tracing tracing = SpringContextUtil.getBean(Tracing.class);
                if (tracing != null) {
                    Map<Object, Object> map = JacksonUtil.parseObject(responseWrapper.getContent(), new TypeReference<Map<Object, Object>>() {
                    });
                    map.put("traceId", tracing.currentTraceContext().get().traceIdString());
                    HttpServletResponse response = servletResponse;
                    if (!response.isCommitted()) {
                        response.reset();//很重要
                    }
                    response.setContentType("text/html;charset = UTF-8");
                    response.getWriter().write(HoAirGsonUtil.objectToJson(map));
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        } else {
            try {
                HttpServletResponse response = servletResponse;
                if (!response.isCommitted()) {
                    response.reset();//很重要
                }
                response.getWriter().write(responseWrapper.getContent());
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }

    private boolean initContext(BRHttpServletRequestWrapper requestWrapper, HttpServletResponse servletResponse) throws IOException {
        try {
            HttpServletRequest request = (HttpServletRequest) requestWrapper.getRequest();
            HOContext.setContext(new HOContext());
            RequestHead requestHead = new RequestHead();
            HOContext.setHead(requestHead);
            //官网渠道使用
            requestHead.channelNo = request.getHeader(Executor.CHANNEL_NO);
            //手机等移动端使用
            if (StringUtils.isBlank(requestHead.channelNo)) {
                requestHead.channelNo = request.getHeader(Executor.CHANNEL_CODE);
            }
            //HOCAR大出行使用
            if (StringUtils.isBlank(requestHead.channelNo)) {
                requestHead.channelNo = request.getHeader(Executor.channel_code);
            }
            requestHead.ffpId = request.getHeader(Executor.FFP_ID);
            requestHead.ffpNo = request.getHeader(Executor.FFP_NO);
            requestHead.clientIp = HoAirIpUtil.getIpAddr(request);
            requestHead.blackBox = request.getHeader(Executor.BLACK_BOX);
            String headChannelNo = requestHead.channelNo;
            String bodyStr = requestWrapper.getBodyStr();
            if (StrUtil.isNotBlank(bodyStr)) {
                Map<String, String> jsonBody = JSONUtil.toBean(bodyStr, new TypeToken<Map<String, String>>() {
                }.getType(), true);
                String ffpId = jsonBody.get(Executor.FFP_ID);
                String ffpNo = jsonBody.get(Executor.FFP_NO);
                String channelNo = StrUtil.isNotBlank(jsonBody.get(Executor.CHANNEL_NO_SMALL)) ? jsonBody.get(Executor.CHANNEL_NO_SMALL) : jsonBody.get(Executor.CHANNEL_NO);
                String clientIp = jsonBody.get(Executor.CLIENT_IP);
                String blackBox = jsonBody.get("blackBox");

                //优先使用body中的参数
                requestHead.ffpId = StringUtils.isNotBlank(ffpId) ? ffpId : requestHead.ffpId;
                requestHead.ffpNo = StringUtils.isNotBlank(ffpNo) ? ffpNo : requestHead.ffpNo;
                requestHead.channelNo = StringUtils.isNotBlank(channelNo) ? channelNo : requestHead.channelNo;
                requestHead.clientIp = StringUtils.isNotBlank(clientIp) ? clientIp : requestHead.clientIp;
                requestHead.blackBox = StringUtils.isNotBlank(blackBox) ? blackBox : requestHead.blackBox;
            }
            if (StrUtil.isBlank(requestHead.channelNo)) {
                return true;
            }
            headChannelNo = requestHead.channelNo;
            String finalHeadChannelNo = headChannelNo;
            requestHead.channelInfo = Optional.of(SpringContextUtil.getBean("commonService", CommonService.class))
                    .map(i -> i.findChannelInfo(finalHeadChannelNo))
                    .orElse(new ChannelInfo());
            return true;
        } catch (OrderServiceException oe) {
            log.error("1.HOContextSetFilter.initContext出现异常，异常原因：", oe);
            ResponseData responseData = ResponseData.fail(oe.getError().getCode(), oe.getError().getStatus(), oe.getError().getMessage());
            writeResponse(servletResponse, responseData);
            return false;
        } catch (ServiceException se) {
            log.error("2.HOContextSetFilter.initContext出现异常，异常原因：", se);
            ResponseData responseData = ResponseData.fail(se.getError().getCode(), se.getError().getStatus(), se.getError().getMessage());
            writeResponse(servletResponse, responseData);
            return false;
        } catch (Exception e) {
            log.error("3.HOContextSetFilter.initContext出现异常，异常原因：", e);
            ResponseData responseData = ResponseData.fail(CommonErrorCode.SYSTEM_ERROR.getCode(), CommonErrorCode.SYSTEM_ERROR.getStatus(), CommonErrorCode.SYSTEM_ERROR.getMessage());
            writeResponse(servletResponse, responseData);
            return false;
        }
    }

    /**
     * 输出信息
     *
     * @param response
     * @param responseData
     * @throws IOException
     */
    private void writeResponse(ServletResponse response, ResponseData responseData) throws IOException {
        response.setCharacterEncoding("UTF-8");
        Tracing tracing = SpringContextUtil.getBean(Tracing.class);
        responseData.setTraceId(tracing.currentTraceContext().get().traceIdString());
        PrintWriter out = response.getWriter();
        out.print(HoAirGsonUtil.objectToJson(responseData));
        out.flush();
        out.close();
    }
}
