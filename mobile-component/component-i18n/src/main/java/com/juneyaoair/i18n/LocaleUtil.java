package com.juneyaoair.i18n;

import com.alibaba.cloud.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

/**
 * <AUTHOR>
 * @description 国际化信息获取  主要方便在代码中使用，不需要每次@Autowired注入MessageSource来使用
 * @date 2024/9/19 10:32
 */
@Component
public class LocaleUtil {

    @Autowired
    private MessageSource messageSource;

    /**
     *
     * @param code 国际化code
     * @return
     */
    public String getTips(String code){
        try{
            return messageSource.getMessage(code,null, LocaleContextHolder.getLocale());
        }catch (Exception e){
            return "";
        }
    }

    /**
     *
     * @param code 国际化code
     * @param lang 语言  格式:zh_CN  语言_国家码
     * @return
     */
    public String getTips(String code,String lang){
        return messageSource.getMessage(code,null, getLocale(lang));
    }

    public String getTips(String code,Object[] objects,String lang){
        return messageSource.getMessage(code,objects, getLocale(lang));
    }

    /**
     * 初始化语言设置
     * @param lang
     * @return
     */
    public static Locale getLocale(String lang){
        // 默认语言版本 简体中文
        Locale locale = Locale.SIMPLIFIED_CHINESE;;
        if (StringUtils.isBlank(lang)) {
            return locale;
        }
        if (lang.contains("_")) {
            return new Locale(lang.split("_")[0], lang.split("_")[1]);
        } else {
            return new Locale(lang);
        }
    }
}
