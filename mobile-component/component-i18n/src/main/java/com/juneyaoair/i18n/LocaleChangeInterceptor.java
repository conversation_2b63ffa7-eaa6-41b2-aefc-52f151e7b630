package com.juneyaoair.i18n;

import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/24 18:52
 */
@Component
@Order(value = 20)
public class LocaleChangeInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String lang = request.getHeader("language");
        LanguageEnum language = LanguageEnum.getLanguage(lang);
        // 获取语言版本
        Locale locale = LocaleUtil.getLocale(language.name());
        // 设置Locale到LocaleContextHolder中，以便在整个请求处理过程中都可以访问
        LocaleContextHolder.setLocale(locale);
        return true;
    }
}
