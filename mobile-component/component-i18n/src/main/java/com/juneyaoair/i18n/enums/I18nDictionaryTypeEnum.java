package com.juneyaoair.i18n.enums;

/**
 * 国际化字典信息
 *      数据参照表：I18N_DICTIONARY
 * <AUTHOR>
 */

public enum I18nDictionaryTypeEnum {

    /**
     * desc
     */
    BUSINESS_DEPARTMENT("营业部_信息"),
    COUNTRY("国家"),
    CABIN_NAME("舱位名称"),
    PLAN_TYPE_NAME("机型名称"),
    FLIGHT_STATUS_BRIDGE_ZH("航班动态_机位描述"),
    FLIGHT_STATUS_FLIGHT_STATUS_ZH("航班动态_航班状态"),
    AIRPORT_EXTRA_INFO("机场额外信息"),
    CERT_EXPIRE_TAG("证件过期标记"),
    ;

    private final String desc;

    I18nDictionaryTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

}
