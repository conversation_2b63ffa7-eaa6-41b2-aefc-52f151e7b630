package com.juneyaoair.oneorder.constant;

/**
 * <AUTHOR>
 * @description 常用的正则表达式
 * @date 2023/6/16 9:18
 */
public class PatternCommon {
    /**
     * 国内手机号
     */
    public static final String MOBILE_PHONE = "^1[3|4|5|6|7|8|9][0-9]{1}[0-9]{8}$";
    /**
     * <AUTHOR>
     * @Description 新版国际手机号（区号非86）：1-16位的数字
     * @Date 9:47 2024/6/17
     **/
    public static final String MOBILE_GLOBAL_MATCH = "^[0-9]{5,16}$";

    /**
     * <AUTHOR>
     * @Description 新版国际手机区号：1-4位的数字
     * @Date 9:47 2024/6/17
     **/
    public static final String TELEPHONE_CODE_MATCH = "^[0-9]{1,4}$";
    /**
     * 手机号兼容国际
     */
    public static final String MOBILE_INTERNATIONAL = "^[0-9]{5,11}$";
    /**
     * 国际手机号标准版 ^\+?\d{1,4}-\d{6,14}$
     * ^ 表示字符串的开始。
     * \+? 匹配可选的加号（国际拨号前缀）。
     * \d{1,4} 匹配1到4位数字，通常用于表示国家代码。
     * - 匹配一个破折号。
     * \d{6,14} 匹配6到14位数字，用于表示手机号码的其余部分。
     * $ 表示字符串的结束。
     **/
    public static final String GLOBAL_PHONE = "^\\d{1,4}-\\d{6,14}$";

    //身份证号
    public static final String ID_NUMBER = "^(\\d{6})(18|19|20)?(\\d{2})([01]\\d)([0123]\\d)(\\d{3})(\\d|X|x)?$";
    //消费密码
    public static final String SALE_P_W_D = "^\\d{6}$";
    //登录密码
    public static final String LOGIN_P_W_D = "^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$";
    //http或https网络请求
    public static final String WEB_HTTP = "(http|https):\\/\\/(\\w+:{0,1}\\w*@)?(\\S+)(:[0-9]+)?(\\/|\\/([\\w#!:.?+=&%@!\\-\\/]))?";
    //邮箱
    public static final String EMAIL = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
    //票号
    public static final String TICKET_NO = "^(018)-?\\d{10}$";
    //日期格式 yyyy-MM-dd
    public static final String DATE_NORMAL = "((19|20)\\d\\d)-(0?[1-9]|1[012])-(0?[1-9]|[12][0-9]|3[01])";
    //性别
    public static final String SEX = "[FM]{1}";
    /**
     * 护照
     */
    public static final String PASSPORT_NO = "^[A-Za-z0-9]{5,11}$";
    /**
     * html数据
     */
    public static final String HTML = "^\\u003chtml xmlns\\u003d[\\s\\S\\w\\W]*\\u003c/html\\u003e\\s*";
    /**
     * 港澳台通行证
     */
    public static final String PERMIT_HKMOTW = "^[a-zA-Z0-9]{5,21}$";
    /**
     * 一个数字
     */
    public static final String ONE_NUMBER = "\\d";
    /**
     * 大写的26个英文字母
     */
    public static final String UPPER_LETTER_26 = "[A-Z]";
    /**
     * 国内国际标志
     */
    public static final String TRIP_TYPE_VALUR = "[DI]{1}";
    /**
     * 旅客姓名正则
     */
    public static final String PASS_NAME_REGEX = "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*)";
    /**
     * 时间格式
     */
    public static final String TIME_REGEX = "^(20|21|22|23|[0-1]\\d):[0-5]\\d";
    /**
     * 港澳居民居住证 2021-03-09
     */
    public static final String HMT_ID_CARD = "^(8[1|2|3]0000)(18|19|20)?(\\d{2})([01]\\d)([0123]\\d)(\\d{3})(\\d|X|x)?$";

    public static final String TOTALTYPE = "Theme.*Coupon";

    /**
     * 中文汉字
     */
    public static final String CHINESE_STRING = "[\\u4e00-\\u9fa5]";
    /**
     * 13位票号检验
     */
    public static final String DIGIT_13_TICKET_REGEXP = "^(\\d{3}-?\\d{10})$";
    /**
     * HO航班号
     */
    public static final String HO_FLIGHT = "(HO)[0-9]{4}";
    /**
     * HO会员卡号校验
     */
    public static final String HO_CARD = "^\\d{8,}$";

    public static final String GV_GROUP="GV\\d";

}
