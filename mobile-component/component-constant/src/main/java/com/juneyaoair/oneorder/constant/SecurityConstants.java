package com.juneyaoair.oneorder.constant;

/**
 * <AUTHOR>
 * @description 权限相关通用常量
 * @date 2023/6/29 14:02
 */
public class SecurityConstants {
    /**
     * 统一请求头部渠道信息
     */
    public static final String HEAD_CHANNEL_NO = "ChannelNo";
    /**
     * 请求头
     */
    public static final String HEAD_CHANNEL_CODE = "channelCode";

    /**
     * 统一渠道版本信息
     */
    public static final String HEAD_CLIENT_VERSION = "ClientVersion";
    /**
     * 头部编译版本信息
     */
    public static final String HEAD_VERSION_CODE = "VersionCode";
    /**
     * 吉祥汽车请求头
     */
    public static final String HEAD_CAR_CHANNEL_CODE = "channel_code";
    /**
     * 吉祥汽车版本号
     */
    public static final String HEAD_VERSION = "version";
    /** 语言 */
    public static final String LANGUAGE = "language";

    /** 操作平台 */
    public static final String PLATFORM_INFO = "platforminfo";
    /**
     * 设备指纹来源，主要是解决类似小程序容器，实际使用的是H5页面指纹
     */
    public static final String BLACKBOX_FROM = "from";
    /** 来源IP */
    public static final String ORIGIN_IP = "originIp";
    /**
     * 会员NO
     */
    public static final String FFP_NO = "ffpNo";

    public static final String FFP_CARD_NO = "ffpCardNo";;

}
