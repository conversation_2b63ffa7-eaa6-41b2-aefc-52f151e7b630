package com.juneyaoair.mobile.exception.errorcode;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/6 9:01
 */
public enum CommonErrorCode implements ErrorCode {
    /** status message */
    SUCCESS(200, "成功"),
    FAIL(200, "访问失败"),
    BUSINESS_ERROR(200, "业务异常!"),
    SYSTEM_ERROR(500, "系统繁忙，请稍后再试"),
    REQUEST_VALIDATION_FAILED(400, "请求数据格式验证失败"),
    TICKET_INFO_NOT_FOUND(400, "未查询到客票信息，请确认信息是否输入正确"),
    LOCK_OCCUPIED(409, "任务已被锁定，请稍后重试"),
    CAPTCHA_SEND_FAIL(500, "验证码下发失败"),
    VERIFY_CODE_ERROR(500, "验证码不正确"),
    VERIFY_CODE_INVALID(500, "验证码已失效"),
    VERIFY_CODE_FAIL(500, "验证码错误或已经失效"),
    CANCEL_SEAT_FAIL(500, "取消选座失败"),
    CHECK_IN_CANCEL_COUNT_LIMIT(500, "值机取消次数超过限制"),
    QUICK_VERIFY_FAIL(500, "安全校验失败"),
    NO_REAL_NAME(500, "您还未实名，请先完成实名认证！"),
    REAL_NAME_EXPIRED(500, "实名认证超过期限"),
    NEED_CONFIRM(500, "为了确保此操作由您本人执行，请完成支付宝实时认证。若无法使用支付宝认证，请联系客服95520或下载吉祥航空APP。"),
    ACCOUNT_WARNING(500, "您的账户存在风险，无法继续操作，如有疑问请咨询官方客服"),
    NOT_LOGIN(200, "您还未登录，请先登录！"),
    INVALID_TOKEN(200, "登录凭证已失效，请重新登录"),
    LOGIN_ERROR(200,"账户或密码不正确"),
    ACCOUNT_NOT_EXIST(200,"账户不存在"),
    CACHE_FAIL(500, "操作超时，请重新查询"),
    OPERATIONAL_TIMEOUT(500, "操作超时，请返回重新查询"),
    NOT_SUPPORT(200, "暂不支持证件号方式，请使用票号查询"),
    NOT_ID_CARD(208, "为了您的账户安全，请前往个人中心-个人信息-会员资料页添加您的身份证信息，完善后可继续操作"),
    OVER_LIMIT(200, "访问超过限制"),
    CHANNEL_ACCESS_FAIL(200, "暂不支持当前渠道访问"),
    DEVICE_IP_ERROR_OVER_LIMIT(200,"设备错误次数已达上限"),
    ACCOUNT_ERROR_OVER_LIMIT(200,"账户错误次数已达上限"),
    DEVICE_OVER_LIMIT(200,"设备操作频繁"),
    ACCOUNT_OVER_LIMIT(200,"账户操作频繁"),
    SEAT_MAP_FAIL(200, "获取航班座位图失败"),
    SEAT_TICKET_INFO_FAIL(200, "获取客票信息失败"),
    VERIFY_PAY_PASSWORD_CHECK_FAIL(200, "消费密码校验不通过"),
    RESERVE_SEAT_FAIL(200, "选座操作失败"),
    CHECK_IN_FAILED(200, "旅客【%s】值机操作失败，请重新办理"),
    OTHER_CHANNEL_SEAT_ORDER(200, "选座操作失败"),
    CANCEL_SEAT_ORDER_FAIL(200, "选座订单取消失败"),
    REFUND_SEAT_ORDER_FAIL(200, "选座订单退单失败"),
    SEAT_SELECT_TIP(200, "取消值机时提示信息"),
    SEAT_SELECT_MAX_TIP(200, "取消值机时最大取消次数提示信息"),
    SEAT_ADD_PEER_FAIL(200, "同行人添加失败"),
    GET_SEAT_CHECK_INFO_FAIL(200, "旅客选座值机信息获取失败"),
    SEAR_TRAVELLER_NO_TRIP(200, "无有效行程信息"),
    REGISTER_FAIL(200, "注册失败"),
    HAS_REGISTER(200, "此账号已存在，请登录"),
    PASSENGER_ADD_FAIL(200, "添加失败"),
    PASSENGER_NAME_LENGTH_ERROR(200, "乘客姓名长度错误，需大于0小于等于25个字符"),
    O_SERVICE_ADD_CONTACT_ERR(200, "新增常用乘机人异常"),
    B2B_TIPS(200, "您所查询的客票为团队客票，请联系您的购票代理人咨询"),
    NOT_SUPPORT_FOREIGN_CURRENCY_PAY(200, "不能使用外币支付"),
    NOT_SUPPORT_CHINESE_CURRENCY_PAY(200, "只能使用外币支付"),
    ;

    private final int status;
    private final String message;

    CommonErrorCode(int status, String message) {
        this.status = status;
        this.message = message;
    }

    @Override
    public int getStatus() {
        return this.status;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public String getCode() {
        return this.name();
    }

}
