# HO-MobileAPI

# 开发前先查看[分支管理说明](http://gitlab.juneyaoair.com:10080/MarktingService/ho-mobileapi/wikis/home)

# Sonar

## 指令

```shell script
mvn sonar:sonar -Dmaven.test.skip=true -Dsonar.projectKey=ho-mobile-bff -Dsonar.host.url=http://sonar.juneyaoair.com:9000  -Dsonar.login=****************************************
```

## 地址

http://sonar.juneyaoair.com:9000/dashboard?id=HO-MOBILE-BFF

## 要求

* 按照要求，安全热点问题全部修改完毕，Bug和漏洞（阻断，严重，主要，次要，提示）全部修改完毕，异味（阻断）所提示的项目，全部关闭

## 端口说明
* searchbff          8091
* orderbff           8092
* mainpage-service   8093
* accountbff-service 8094
* bookbff-service    8095
* cuss-service    8096
* mobile-hocar-service    9092

## gateway filter 执行顺序 数值越小越先执行  先WebFilter后GlobalFilter
## webFilter执行顺序  
CorsWebFilter    -90
RequestCoverFilter  -80
XssFilter -50
SaReactorFilter 10
AuthorityFilter 100
## GlobalFilter执行顺序
## 创建一个GatewayFilter，必须实现Ordered接口，返回一个小于-1的order值，这是因为NettyWriteResponseFilter的order值为-1，我们需要覆盖返回响应体的逻辑，自定义的GlobalFilter必须比NettyWriteResponseFilter优先执行
LogFilter -70
PortalFilter -60


## HandlerInterceptor执行顺序 值越小，优先级越高
HeaderInterceptor               1
HOContextInterceptor            10  
LocaleChangeInterceptor         20

## AOP优先级 值越小，优先级越高
LogAspect                       1000
ChannelAspect                   Integer.MAX_VALUE

## 项目开发文档说明
https://kdocs.cn/l/cb13WTpM8htX

