package com.juneyaoair.oneorder.accountbff.biz;

import com.juneyaoair.oneorder.tools.utils.RSAUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/2 9:54
 */
@Slf4j
public class LuggageAppBizTest {
    @Test
    public void toLuggageAppTest() throws Exception {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("ffpCardNo", "**********");
        paramMap.put("ffpId", "3498472");
        paramMap.put("timestamp", String.valueOf(System.currentTimeMillis()));
        Map<String, String> keyPair = RSAUtil.generateKeyPair();
        LuggageAppBiz luggageAppBiz = new LuggageAppBiz();
        Map<String,String> param = luggageAppBiz.buildLuggageAppParam(paramMap,keyPair.get("privateKey"),keyPair.get("publicKey"),"https://m.juneyaoair.com");
        log.error("返回结果:{}",param);
    }

}