package com.juneyaoair.oneorder.accountbff.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * 附加服务枚举
 */
public enum CouponSourceEnum {

    /**
     * 贵宾休息室
     */
    Lounge("Lounge", "贵宾休息室券"),

    /**
     * 预留登机牌
     */
    CheckinSubstitution("CheckinSubstitution", "预留登机牌费"),

    /**
     * 文件速递
     */
    MailTravel("MailTravel", "邮寄行程单快递费"),

    /**
     * 逾重行李额
     */
    Baggage("Baggage", "逾重行李券"),

    /**
     * 升舱
     */
    Upgrade("Upgrade", "升舱券"),

    /**
     * WIFI
     */
    WIFI("WIFI", "WIFI费"),

    /**
     * 电话卡
     */
    PhoneCard("PhoneCard", "电话卡费"),

    /**
     * 签证
     */
    VISA("VISA", "签证费"),

    /**
     * 接送机
     */
    Traffic("Traffic", "接送费"),

    /**
     * 无限升舱卡
     */
    UPGRADEUNLIMITED("UpgradeUnlimited","无限升舱卡"),

    CHILDUNLIMITEDFLY("ChildUnlimitedFly","儿童畅飞卡"),

    ADULTUNLIMITEDFLY("AdultUnlimitedFly","成人畅飞卡"),
    /**
     * 吉祥畅飞卡2.0 2020-11-13
     */
    UNLIMITED_FLY_V2("UnlimitedFly","吉祥畅飞卡2.0"),
    /**
     * 吉祥畅飞卡2.0 春运版 2020-11-13
     */
    UNLIMITED_FLY_V2_SF("UnlimitedFlySF","吉祥畅飞卡2.0 春运版"),
    THEME_SEAFOOD_COUPON("ThemeSeafoodCoupon","主题卡-海鲜卡"),
    THEME_MILKTEA_COUPON("ThemeMilkTeaCoupon","主题卡-奶茶卡"),
    THEME_CHERRY_COUPON("ThemeCherryCoupon","主题卡-樱花卡"),
    THEME_OUTHIKE_COUPON("ThemeOutHikeCoupon","主题卡-踏青卡"),
    THEME_HOTPOT_COUPON("ThemeHotPotCoupon","主题卡-火锅卡"),
    THEME_VERMIC_COUPON("ThemeVermicCoupon","主题卡-嗦粉卡"),
    THEME_SEALAND_COUPON("ThemeSeaLandCoupon","主题卡-海岛卡"),
    THEME_SKITRIP_COUPON("ThemeSkiTripCoupon","主题卡-滑雪卡"),
    /**
     * 固包
     */
    Package("Package", "固包费"),

    ONBOARDPRODUCT("OnboardProduct", "机上购物"),

    BRANDMEALS("BrandMeals", "品牌餐食"),

    COUPON("Coupon","优惠券"), //打包优惠券

    RESCHEDULE("Reschedule","改期券"), //新产品平台

    AIRPLANE_UPGRADE("AirplaneUpgrade", "机上升舱"),
    UPGRADECOUPON("UpgradeCoupon","升舱券"), //新产品平台
    LOUNGECOUPON("LoungeCoupon","贵宾休息室"), //新产品平台
    BAGGAGECOUPON("BaggageCoupon","逾重行李券"), //新产品平台
    EXTRABAGGAGE("ExtraBaggage","运输服务-逾重行李费"), //新产品平台 付费行李
    PAYSEAT("PaySeat","物流辅助服务-服务费"),
    THEMEHOTPOTCOUPON("ThemeHotPotCoupon","火锅卡"),
    THEMEVERMICELLICOUPON("ThemeVermicelliCoupon","嗦粉卡"),
    THEMEOUTHIKECOUPON("ThemeOutHikeCoupon","踏青卡"), //新产品平台 付费选座
    EXCESSBAGGAGEINVOICE("ExcessBaggageInvoice","逾重行李发票");//逾重行李发票费


    private String code;

    private String couponName;

    CouponSourceEnum(String code, String couponName) {
        this.code = code;
        this.couponName = couponName;
    }

    public static CouponSourceEnum getByCode(String code){
        CouponSourceEnum couponSourceEnum = null;
        for (CouponSourceEnum couponSource : CouponSourceEnum.values()) {
            if (StringUtils.equals(code, couponSource.code)) {
                couponSourceEnum = couponSource;
                break;
            }
        }
        return couponSourceEnum;
    }

    public String getCode() {
        return code;
    }

    public String getCouponName() {
        return couponName;
    }
}
