package com.juneyaoair.oneorder.bookbff.dto;

import com.juneyaoair.oneorder.common.dto.GeetestDto;
import com.juneyaoair.oneorder.dto.prepaybaggage.QueryPrepaymentBaggageSegmentReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class QueryFlightReq extends GeetestDto {

    @ApiModelProperty(
            value = "票号/身份证号",
            name = "ticketNo",
            required = true
    )
    public String ticketNo;
    @ApiModelProperty(
            value = "姓名",
            name = "name",
            required = true
    )
    public String name;
    @ApiModelProperty(
            value = "航班号",
            name = "flightNo",
            required = true
    )
    public String flightNo;
    @ApiModelProperty(
            value = "航班日期yyyy-MM-dd",
            name = "flightDate",
            required = true
    )
    public String flightDate;
    @ApiModelProperty(
            value = "出发城市",
            name = "depCityCode",
            required = true
    )
    public String depCityCode;
    @ApiModelProperty(
            value = "达到城市",
            name = "arrCityCode",
            required = true
    )
    public String arrCityCode;

    public QueryPrepaymentBaggageSegmentReq toQueryPrepaymentBaggageSegmentReq() {
        QueryPrepaymentBaggageSegmentReq ret = new QueryPrepaymentBaggageSegmentReq();
        ret.setTicketNo(this.ticketNo);
        ret.setName(this.name);
        ret.setFlightNo(this.flightNo);
        ret.setFlightDate(this.flightDate);
        ret.setDepCityCode(this.depCityCode);
        ret.setArrCityCode(this.arrCityCode);
        return ret;
    }
}
