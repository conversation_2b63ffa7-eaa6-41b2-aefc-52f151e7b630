package com.juneyaoair.oneorder.searchbff.service;

import com.juneyaoair.oneorder.amap.request.AmapInputTipsParam;
import com.juneyaoair.oneorder.amap.response.AmapInputTipsResult;
import com.juneyaoair.oneorder.amap.response.AmapIpResult;

/**
 * <AUTHOR>
 * @Description 对接高德
 * @created 2024/4/8 14:34
 */
public interface AmapService {

    /**
     * 高德地图-IP定位
     * @param ip
     * @return
     */
    AmapIpResult amapIp(String ip);

    /**
     * 高德地图-输入提示
     * @param amapInputTipsParam
     * @return
     */
    AmapInputTipsResult amapInputTips(AmapInputTipsParam amapInputTipsParam);

}
