package com.juneyaoair.oneorder.searchbff.controller;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.flightbasic.response.api.HolidayCalendar;
import com.juneyaoair.oneorder.annotation.ApiLog;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.common.service.TongDunService;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.fare.dto.display.*;
import com.juneyaoair.oneorder.fare.dto.display.airlinecerttype.AirlineCertTypeReqDTO;
import com.juneyaoair.oneorder.fare.dto.display.airlinecerttype.AirlineCertTypeResDTO;
import com.juneyaoair.oneorder.fare.dto.display.refund.InsuranceRefundReqDTO;
import com.juneyaoair.oneorder.fare.dto.display.refund.InsuranceRefundResDTO;
import com.juneyaoair.oneorder.fare.dto.display.ticketdelivery.TicketDeliveryFareReqDTO;
import com.juneyaoair.oneorder.fare.dto.display.ticketdelivery.TicketDeliveryFareResDTO;
import com.juneyaoair.oneorder.fare.feign.FareClient;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.restresult.response.SuccessResponseData;
import com.juneyaoair.oneorder.searchbff.constant.SearchBffConstant;
import com.juneyaoair.oneorder.tools.constant.AirConstants;
import com.juneyaoair.oneorder.tools.enums.EnumInterNationTag;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运价Controller
 *
 * <AUTHOR>
 * @date 2023/4/18 15:12
 */
@Api(value = "运价服务", tags = "运价服务")
@Slf4j
@RestController
public class FareController extends BaseController {

    @Resource
    private FareClient fareClient;

    @Resource
    private TongDunService tongDunService;


    @Resource
    private HttpServletRequest httpServletRequest;


    @Value("${oneorder.lowPriceSwitch:N}")
    private String lowPriceSwitch;

    @Value("${oneorder.tongDunDSwitch:N}")
    private String tongDunDSwitch;


    @Value("${oneorder.i18n.tongDunSwitch:N}")
    private String tongDunSwitchI18n;


    @Value("${oneorder.tongDunISwitch:N}")
    private String tongDunISwitch;


    @Value("${oneorder.tongDunPermitIpPrefix0:172}")
    private String tongDunPermitIpPrefix0;


    @Resource
    private CacheService cacheService;

    @Autowired
    private IBasicService basicService;

    // 移动端国内运价同盾开关
    @Value("${oneorder.mobile.dom.tongDunSwitch:N}")
    private String mobileDomTongDunSwitch;

    // 移动端国际运价同盾开关
    @Value("${oneorder.mobile.inter.tongDunSwitch:N}")
    private String mobileInterTongDunSwitch;

    // 鸿蒙运价同盾开关
    @Value("${oneorder.harmonyOs.tongDunSwitch:N}")
    private String harmonyOsTongDunSwitch;

    /**
     * 国际运价简要查询
     *
     * @param requestData 运价入参
     * @return 运价结果
     */
    @PostMapping("/QueryInterFlightSimple")
    public ResponseData<QueryFlightInfoResDTO> queryInterFlightSimple(@RequestBody RequestData<QueryFlightInfoReqDTO> requestData) {

        ResponseData<QueryFlightInfoResDTO> responseData = fareClient.queryInterFlightSimple(requestData);

        return responseData;
    }

    /**
     * 国际运价详情查询
     *
     * @param requestData 运价入参
     * @return 运价结果
     */
    @PostMapping("/QueryInterFlightDetail")
    public ResponseData<QueryFlightInfoResDTO> queryInterFlightDetail(@RequestBody RequestData<QueryFlightInfoReqDTO> requestData) {

        ResponseData<QueryFlightInfoResDTO> responseData = fareClient.queryInterFlightDetail(requestData);

        return responseData;
    }


    /**
     * 航班信息国际化查询
     */
    @PostMapping("/QueryFlightInfoI18n")
    @ApiOperation(value = "航班信息国际化查询", notes = "航班信息国际化查询")
    ResponseData<QueryFlightInfoResDTO> queryFlightInfoI18n(@RequestBody @Validated RequestData<QueryFlightInfoReqDTO> requestData) {

        if ("Y".equals(tongDunSwitchI18n)) {
            //同盾逻辑
            String blackBox = httpServletRequest.getHeader("BlackBox");
            requestData.getData().setBlackBox(blackBox);
            tongDunService.fareAntiFraud(requestData, initBizDto(httpServletRequest));
        }
        return fareClient.queryFlightInfoI18n(requestData);
    }


    /**
     * 舱位信息国际化查询
     */
    @PostMapping("/QueryCabinInfoI18n")
    @ApiOperation(value = "舱位信息国际化查询", notes = "舱位信息国际化查询")
    ResponseData<QueryFlightInfoResDTO> queryCabinInfoI18n(@RequestBody @Validated RequestData<QueryFlightInfoReqDTO> requestData) {

        if ("Y".equals(tongDunSwitchI18n)) {
            //同盾逻辑
            String blackBox = httpServletRequest.getHeader("BlackBox");
            requestData.getData().setBlackBox(blackBox);
            tongDunService.fareAntiFraud(requestData, initBizDto(httpServletRequest));
        }
        return fareClient.queryCabinInfoI18n(requestData);
    }


    /**
     * 查询汇率
     *
     * @param requestData
     * @return
     */
    @PostMapping("/GetRate")
    @ApiOperation(value = "查询汇率", notes = "查询汇率")
    public ResponseData<QueryRateResponseDTO> getRate(@RequestBody RequestData<QueryRateRequestDTO> requestData) {

        return fareClient.getRate(requestData);
    }

    /**
     * 国内运价简要查询
     *
     * @param req 运价入参
     * @return 运价结果
     */
    @PostMapping("/QueryDomFlightSimple")
    public ResponseData<QueryFlightInfoResDTO> queryDomFlightSimple(@RequestBody RequestData<QueryFlightInfoReqDTO> req) {
        log.info(this.getClass().getName() + "-queryDomFlightSimple-请求入参req={}", JSON.toJSONString(req));
        ResponseData<QueryFlightInfoResDTO> responseData = fareClient.queryDomFlightSimple(req);
        return responseData;
    }

    /**
     * 国内运价详情查询
     *
     * @param req 运价入参
     * @return 运价结果
     */
    @PostMapping("/QueryDomFlightDetail")
    public ResponseData<QueryFlightInfoResDTO> queryDomFlightDetail(@RequestBody RequestData<QueryFlightInfoReqDTO> req) {
        log.info(this.getClass().getName() + "-queryDomFlightDetail-请求入参req={}", JSON.toJSONString(req));
        ResponseData<QueryFlightInfoResDTO> responseData = fareClient.queryDomFlightDetail(req);
        return responseData;
    }

    /**
     * 运价简要查询
     *
     * @param requestData 运价入参
     * @return 运价结果
     */
    @ApiLog
    @PostMapping("/QueryFlightSimple")
    @ApiOperation(value = "运价简要查询", notes = "运价简要查询")
    public ResponseData<QueryFlightInfoResDTO> queryFlightSimple(@RequestBody RequestData<QueryFlightInfoReqDTO> requestData) {

        if (checkCityCode(requestData)) {
            return new SuccessResponseData();
        }

        //同盾逻辑
        String blackBox = httpServletRequest.getHeader("BlackBox");
        requestData.getData().setBlackBox(blackBox);
        tongDunService.fareAntiFraud(requestData, initBizDto(httpServletRequest));

        ResponseData<QueryFlightInfoResDTO> responseData = fareClient.queryFlightSimple(requestData);
        return responseData;
    }

    /**
     * 运价详情查询
     *
     * @param requestData 运价入参
     * @return 运价结果
     */
    @ApiLog
    @PostMapping("/QueryFlightDetail")
    @ApiOperation(value = "运价详情查询", notes = "运价详情查询")
    public ResponseData<QueryFlightInfoResDTO> queryFlightDetail(@RequestBody RequestData<QueryFlightInfoReqDTO> requestData) {

        if (checkCityCode(requestData)) {
            return new SuccessResponseData();
        }

        //同盾逻辑
        String blackBox = httpServletRequest.getHeader("BlackBox");
        requestData.getData().setBlackBox(blackBox);
        tongDunService.fareAntiFraud(requestData, initBizDto(httpServletRequest));

        ResponseData<QueryFlightInfoResDTO> responseData = fareClient.queryFlightDetail(requestData);
        return responseData;
    }

    /**
     * 低价日历查询
     *
     * @param requestData
     * @return
     */
    @PostMapping("/QueryLowPriceInfo")
    @ApiOperation(value = "低价日历查询", notes = "低价日历查询")
    public ResponseData<QueryLowPriceCalResDTO> queryLowPriceInfo(@RequestBody RequestData<QueryLowPriceCalReqDTO> requestData) {
        ResponseData<QueryLowPriceCalResDTO> responseData = new SuccessResponseData();

        if (checkLowPriceCityCode(requestData)) {
            responseData.setData(new QueryLowPriceCalResDTO());
            return responseData;
        }
        if ("Y".equals(lowPriceSwitch)) {
            responseData = fareClient.queryLowPriceInfo(requestData);
        } else {
            responseData.setData(new QueryLowPriceCalResDTO());
        }
        return responseData;
    }


    /**
     * 查询保险信息列表
     *
     * @param req
     * @return
     */
    @PostMapping("/QueryInsureList")
    @ApiOperation(value = "查询保险信息列表", notes = "查询保险信息列表")
    public ResponseData<QueryInsuranceResDTO> queryInsureList(@RequestBody RequestData<QueryInsuranceReqDTO> req) {
        ResponseData<QueryInsuranceResDTO> responseData = fareClient.queryInsureList(req);
        return responseData;
    }


    /**
     * 可购保险查询接口
     *
     * @param req
     * @return
     */
    @PostMapping("/InsuranceValidQuery")
    @ApiOperation(value = "可购保险查询接口", notes = "可购保险查询接口")
    public ResponseData<QueryInsuranceResDTO> queryInsuranceInfo(@RequestBody RequestData<QueryInsuranceReqDTO> req) {
        ResponseData<QueryInsuranceResDTO> responseData = fareClient.queryInsuranceInfo(req);
        return responseData;
    }

    /**
     * 单独投保入库接口
     *
     * @param req
     * @return
     */
    @PostMapping("/InsuranceBook")
    @ApiOperation(value = "单独投保入库接口", notes = "单独投保入库接口")
    public ResponseData<BookInsuranceResDTO> insuranceApply(@RequestBody RequestData<BookInsuranceReqDTO> req) {
        ResponseData<BookInsuranceResDTO> responseData = fareClient.insuranceApply(req);
        return responseData;
    }


    /**
     * 单独退保
     *
     * @param req
     * @return
     */
    @PostMapping("/InsuranceRefund")
    @ApiOperation(value = "单独退保", notes = "单独退保")
    public ResponseData<InsuranceRefundResDTO> refundInsuranceInfo(@RequestBody RequestData<InsuranceRefundReqDTO> req) {
        ResponseData<InsuranceRefundResDTO> responseData = fareClient.refundInsuranceInfo(req);
        return responseData;
    }

    /**
     * 根据航线查询可用证件
     *
     * @param requestData
     * @return
     */
    @PostMapping("/QueryAirlineCertType")
    @ApiOperation(value = "根据航线查询可用证件", notes = "根据航线查询可用证件")
    public ResponseData<AirlineCertTypeResDTO> queryCertTypeByAirline(@RequestBody RequestData<AirlineCertTypeReqDTO> requestData) {
        ResponseData<AirlineCertTypeResDTO> responseData = fareClient.queryCertTypeByAirline(requestData);
        return responseData;
    }

    /**
     * 查询邮寄凭证费用
     *
     * @param requestData
     * @return
     */
    @PostMapping("/QueryTicketDeliveryFare")
    @ApiOperation(value = "查询邮寄凭证费用", notes = "查询邮寄凭证费用")
    public ResponseData<TicketDeliveryFareResDTO> queryTicketDeliveryFare(@RequestBody RequestData<TicketDeliveryFareReqDTO> requestData) {
        ResponseData<TicketDeliveryFareResDTO> responseData = fareClient.queryTicketDeliveryFare(requestData);
        return responseData;
    }

    /**
     * 查询节假日日历
     *
     * @return
     */
    @GetMapping("/QueryHolidayCalender")
    @ApiOperation(value = "查询节假日日历", notes = "查询节假日日历")
    public ResponseData<List<HolidayCalendar>> queryHolidayCalender() {
        return ResponseData.success(basicService.toCatchHolidayCalender());
    }


    /***
     * 鸿蒙运价航班那信息查询
     * @param requestData 运价入参
     * @return 运价结果
     */
    @PostMapping("/QueryFlightInfo")
    public ResponseData<QueryFlightInfoResDTO> queryFlightInfo(@RequestBody RequestData<QueryFlightInfoReqDTO> requestData) {

        // 移动端校验city是否存在、校验是否是国际航线
        Map<String, Boolean> map = checkMobileCityCode(requestData);
        if (map.containsKey(SearchBffConstant.IS_NULL) && map.get(SearchBffConstant.IS_NULL)) {
            return new SuccessResponseData();
        }
        // 判断是否使用同盾校验
        boolean isOpenTongDun = judgeIsOpenTongDun(map);
        if (isOpenTongDun) {
            //同盾逻辑
            String blackBox = httpServletRequest.getHeader("BlackBox");
            requestData.getData().setBlackBox(blackBox);
            tongDunService.fareAntiFraud(requestData, initBizDto(httpServletRequest));
        }

        ResponseData<QueryFlightInfoResDTO> responseData = fareClient.queryFlightInfo(requestData);

        return responseData;
    }

    /***
     * 鸿蒙运价详情查询
     * @param requestData 运价入参
     * @return 运价结果
     */
    @ApiLog
    @PostMapping("/QueryCabinFareInfo")
    public ResponseData<QueryFlightInfoResDTO> queryCabinFareInfo(@RequestBody RequestData<QueryFlightInfoReqDTO> requestData) {

        // 移动端校验city是否存在、校验是否是国际航线
        Map<String, Boolean> map = checkMobileCityCode(requestData);
        if (map.containsKey(SearchBffConstant.IS_NULL) && map.get(SearchBffConstant.IS_NULL)) {
            return new SuccessResponseData();
        }
        // 判断是否使用同盾校验
        boolean isOpenTongDun = judgeIsOpenTongDun(map);
        if (isOpenTongDun) {
            //同盾逻辑
            String blackBox = httpServletRequest.getHeader("BlackBox");
            requestData.getData().setBlackBox(blackBox);
            tongDunService.fareAntiFraud(requestData, initBizDto(httpServletRequest));
        }

        ResponseData<QueryFlightInfoResDTO> responseData = fareClient.queryCabinFareInfo(requestData);
        return responseData;
    }


    /**
     * 国际官网积分兑换规则
     *
     * @param requestData
     * @return
     */
    @PostMapping("/GetSoreRateRule")
    @ApiOperation(value = "国际官网积分兑换规则", notes = "国际官网积分兑换规则")
    ResponseData<QueryScoreRuleRes> getSoreRateRule(@RequestBody RequestData<QueryScoreRuleReq> requestData) {

        return fareClient.getSoreRateRule(requestData);


    }


    /**
     * 国际官网低价日历
     *
     * @param requestData
     * @return
     */
    @PostMapping("/QueryLowPriceInfoI18n")
    @ApiOperation(value = "国际官网低价日历", notes = "国际官网低价日历")
    ResponseData<QueryLowPriceCalResDTO> queryLowPriceInfoI18n(@RequestBody @Validated RequestData<QueryLowPriceCalReqDTO> requestData) {

        return fareClient.queryLowPriceInfoI18n(requestData);

    }

    /***
     * 国际官网根据航线查询可用证件
     * @param requestData
     * @return
     */
    @PostMapping("/QueryAirlineCertTypeI18n")
    @ApiOperation(value = "国际官网根据航线查询可用证件", notes = "国际官网根据航线查询可用证件")
    public ResponseData<AirlineCertTypeResDTO> queryCertTypeByAirlineI18n(@RequestBody RequestData<AirlineCertTypeReqDTO> requestData) {
        ResponseData<AirlineCertTypeResDTO> responseData = fareClient.queryCertTypeByAirlineI18n(requestData);
        return responseData;
    }

    /**
     * @param segCondList
     * @return
     */
    private String inOrDo(List<SegmentDTO> segCondList) {

        for (SegmentDTO segmentDTO : segCondList) {
            boolean f = null == cacheService.getLocalCity(segmentDTO.getDepCity()) || null == cacheService.getLocalCity(segmentDTO.getArrCity()) || !AirConstants.ChinaCode.equals(cacheService.getLocalCity(segmentDTO.getDepCity()).getCountryCode()) || !AirConstants.ChinaCode.equals(cacheService.getLocalCity(segmentDTO.getArrCity()).getCountryCode());
            if (f) {
                return "I";
            } else {
                return "D";
            }
        }
        return null;
    }


    /**
     * 校验city是否存在
     *
     * @param requestData
     * @return
     */
    private boolean checkCityCode(RequestData<QueryFlightInfoReqDTO> requestData) {
        for (SegmentDTO segmentDTO : requestData.getData().getSegCondList()) {
            if (cacheService.getLocalCity(segmentDTO.getDepCity()) == null
                    || cacheService.getLocalCity(segmentDTO.getArrCity()) == null) {
                return true;
            }
        }
        return false;
    }

    /***
     * 移动端校验city是否存在
     * 校验是否是国际航线
     * @param requestData
     * @return
     */
    private Map<String, Boolean> checkMobileCityCode(RequestData<QueryFlightInfoReqDTO> requestData) {
        Map<String, Boolean> returnMap = new HashMap<>();
        for (SegmentDTO segmentDTO : requestData.getData().getSegCondList()) {
            ApiCityInfoDto depCity = cacheService.getLocalCity(segmentDTO.getDepCity());
            ApiCityInfoDto arrCity = cacheService.getLocalCity(segmentDTO.getArrCity());

            boolean isNull = depCity == null || arrCity == null;
            returnMap.put(SearchBffConstant.IS_NULL, isNull);

            if (!isNull) {
                boolean isInter = EnumInterNationTag.I.name().equals(depCity.getIsInternational()) || EnumInterNationTag.I.name().equals(arrCity.getIsInternational());
                returnMap.put(SearchBffConstant.IS_INTER, isInter);
            }
        }
        log.info("checkMobileCityCode-returnMap={}", returnMap);
        return returnMap;
    }

    /***
     * 判断是否使用同盾校验
     * @param map
     * @return
     */
    private boolean judgeIsOpenTongDun(Map<String, Boolean> map) {
        return (AirConstants.Y.equalsIgnoreCase(mobileDomTongDunSwitch) && map.containsKey(SearchBffConstant.IS_INTER) && !map.get(SearchBffConstant.IS_INTER)) ||
                (AirConstants.Y.equalsIgnoreCase(mobileInterTongDunSwitch) && map.containsKey(SearchBffConstant.IS_INTER) && map.get(SearchBffConstant.IS_INTER));
    }

    /**
     * 校验city是否存在
     *
     * @param requestData
     * @return
     */
    private boolean checkLowPriceCityCode(RequestData<QueryLowPriceCalReqDTO> requestData) {
        QueryLowPriceCalReqDTO reqDTO = requestData.getData();
        if (cacheService.getLocalCity(reqDTO.getDepCity()) == null
                || cacheService.getLocalCity(reqDTO.getArrCity()) == null) {
            return true;
        }
        return false;
    }


    /**
     * 是否开启国内同盾功能
     *
     * @param inOrDo
     * @return
     */
    private boolean ifTongDunD(String inOrDo) {
        return "D".equals(inOrDo) && "Y".equals(tongDunDSwitch);
    }


    /**
     * 是否开启国际同盾功能
     *
     * @param inOrDo
     * @return
     */
    private boolean ifTongDunI(String inOrDo) {
        return "I".equals(inOrDo) && "Y".equals(tongDunISwitch);
    }

}
