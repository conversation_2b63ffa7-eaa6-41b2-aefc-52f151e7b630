package com.juneyaoair.oneorder.searchbff.controller;

import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.airtransfer.AirTransferFlightInfo;
import com.juneyaoair.oneorder.airtransfer.AirTransferFlightQuery;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.searchbff.service.AirTransferService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 接送机
 * @created 2024/3/28 16:05
 */
@Api(value = "接送机")
@RestController
public class AirTransferController {

    @Autowired
    private AirTransferService airTransferService;

    @ApiOperation(value = "接送机-航班查询")
    @RequestMapping(value = "/airTransfer/queryFlightInfo", method = RequestMethod.POST)
    public ResponseData<List<AirTransferFlightInfo>> queryFlightInfo(@RequestBody @Validated RequestData<AirTransferFlightQuery> requestBaseResp) {
        AirTransferFlightQuery airTransferFlightQuery = requestBaseResp.getData();
        if (StringUtils.isBlank(airTransferFlightQuery.getFlightNo()) && StringUtils.isAnyBlank(airTransferFlightQuery.getDepCity(), airTransferFlightQuery.getArrCity())) {
            throw new ServiceException("航班号和城市信息不能同时为空");
        }
        // 查询航班信息
        List<AirTransferFlightInfo> airTransferFlightInfoList = airTransferService.queryFlightInfo(airTransferFlightQuery);
        return ResponseData.success(airTransferFlightInfoList);
    }

}
