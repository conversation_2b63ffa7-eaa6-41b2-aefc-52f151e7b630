package com.juneyaoair.oneorder.searchbff.controller;

import com.juneyaoair.oneorder.amap.request.AmapInputTipsParam;
import com.juneyaoair.oneorder.amap.response.AmapInputTipsResult;
import com.juneyaoair.oneorder.amap.response.AmapIpResult;
import com.juneyaoair.oneorder.common.util.IPUtil;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.searchbff.service.AmapService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Description 对接高德
 * @created 2024/4/8 14:33
 */
@Api(value = "对接高德")
@Slf4j
@RestController
public class AmapController {

    @Autowired
    private AmapService amapService;

    @ApiOperation(value = "高德地图-IP定位")
    @PostMapping(value = "/amap/ip")
    public ResponseData<AmapIpResult> amapIp(@RequestBody @Validated RequestData<Object> requestBaseResp, HttpServletRequest request) {
        String ip = IPUtil.getIpAddr(request);
        AmapIpResult amapIpResult = amapService.amapIp(ip);
        return ResponseData.success(amapIpResult);
    }

    @ApiOperation(value = "高德地图-输入提示")
    @PostMapping(value = "/amap/assistant/inputTips")
    public ResponseData<AmapInputTipsResult> amapInputTips(@RequestBody @Validated RequestData<AmapInputTipsParam> requestBaseResp) {
        AmapInputTipsResult amapInputTipsResult = amapService.amapInputTips(requestBaseResp.getData());
        return ResponseData.success(amapInputTipsResult);
    }
}
