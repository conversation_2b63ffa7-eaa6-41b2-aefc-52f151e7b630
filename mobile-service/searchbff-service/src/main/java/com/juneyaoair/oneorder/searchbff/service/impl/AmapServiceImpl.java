package com.juneyaoair.oneorder.searchbff.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Maps;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.amap.request.AmapInputTipsParam;
import com.juneyaoair.oneorder.amap.response.AmapInputTipsResult;
import com.juneyaoair.oneorder.amap.response.AmapIpResult;
import com.juneyaoair.oneorder.searchbff.config.SearchBffConfig;
import com.juneyaoair.oneorder.searchbff.constant.SearchBffConstant;
import com.juneyaoair.oneorder.searchbff.service.AmapService;
import com.juneyaoair.oneorder.tools.utils.HttpClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description 对接高德
 * @created 2024/4/8 14:34
 */
@Slf4j
@Service
public class AmapServiceImpl implements AmapService {

    /** MAP_TYPE_REFERENCE */
    private static final TypeReference<Map<String, String>> MAP_TYPE_REFERENCE = new TypeReference<Map<String, String>>(){};

    @Autowired
    private SearchBffConfig searchBffConfig;

    @Override
    public AmapIpResult amapIp(String ip) {
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("key", searchBffConfig.getAmapKey());
        paramMap.put("ip", ip);
        String url = searchBffConfig.getAmapUrl() + SearchBffConstant.AMAP_IP_V3;
        try {
            String resBody = HttpClientUtils.doGet(url, paramMap);
            return JSON.parseObject(resBody, AmapIpResult.class);
        } catch (Exception e) {
            log.error("调用高德地图-IP地位异常，请求地址：{} 请求参数：{} 异常原因：", url, JSON.toJSONString(paramMap), e);
            throw new ServiceException("IP定位失败");
        }
    }

    @Override
    public AmapInputTipsResult amapInputTips(AmapInputTipsParam amapInputTipsParam) {
        String jsonString = JSON.toJSONString(amapInputTipsParam);
        Map<String, String> paramMap = JSON.parseObject(jsonString, MAP_TYPE_REFERENCE);
        paramMap.put("key", searchBffConfig.getAmapKey());
        String url = searchBffConfig.getAmapUrl() + SearchBffConstant.AMAP_INPUT_TIPS_V3;
        try {
            String resBody = HttpClientUtils.doGet(url, paramMap);
            return JSON.parseObject(resBody, AmapInputTipsResult.class);
        } catch (Exception e) {
            log.error("调用高德地图-输入提示异常，请求地址：{} 请求参数：{} 异常原因：", url, JSON.toJSONString(paramMap), e);
            throw new ServiceException("输入提示失败");
        }
    }

}
