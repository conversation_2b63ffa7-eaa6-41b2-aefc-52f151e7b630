package com.juneyaoair.oneorder.searchbff.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.juneyaoair.oneorder.order.dto.b2corder.orderdetail.AirCompany;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description SearchBff配置文件
 * @created 2024/3/29 9:26
 */
@Data
@Configuration
public class SearchBffConfig {

    /** 航司列表 */
    @ApolloJsonValue("${com.juneyaoair.config.airCompany:{}}")
    private Map<String, AirCompany> airCompanyMap;

    /** 高德地图服务地址 */
    @Value("${searchbff.amap.url:https://restapi.amap.com}")
    private String amapUrl;

    /** 高德地图Key */
    @Value("${searchbff.amap.key:2a12d00b0191d55cd75f8676e0662437}")
    private String amapKey;

}
