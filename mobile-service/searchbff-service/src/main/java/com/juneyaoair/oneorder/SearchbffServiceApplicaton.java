package com.juneyaoair.oneorder;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class}, scanBasePackages = {"com.juneyaoair"})
@EnableDiscoveryClient
@EnableApolloConfig(value = {"searchbff", "application"})
@EnableFeignClients(basePackages = {"com.juneyaoair"})
public class SearchbffServiceApplicaton {
    public static void main(String[] args) {
        SpringApplication.run(SearchbffServiceApplicaton.class, args);
    }
}
