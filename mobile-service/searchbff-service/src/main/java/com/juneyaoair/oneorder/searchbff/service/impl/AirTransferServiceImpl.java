package com.juneyaoair.oneorder.searchbff.service.impl;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.request.flightInfo.FlightInfoReqDTO;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.flightInfo.FlightInfoDTO;
import com.juneyaoair.flightbasic.utils.BaseRequestUtil;
import com.juneyaoair.oneorder.airtransfer.AirTransferFlightInfo;
import com.juneyaoair.oneorder.airtransfer.AirTransferFlightQuery;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.core.context.SecurityContextHolder;
import com.juneyaoair.oneorder.localcache.Segment;
import com.juneyaoair.oneorder.order.dto.b2corder.orderdetail.AirCompany;
import com.juneyaoair.oneorder.searchbff.config.SearchBffConfig;
import com.juneyaoair.oneorder.searchbff.service.AirTransferService;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 接送机
 * @created 2024/3/28 9:23
 */
@Service
public class AirTransferServiceImpl extends CommonService implements AirTransferService {

    @Autowired
    private SearchBffConfig searchBffConfig;

    @Autowired
    private IBasicService basicService;
    @Resource
    private CacheService cacheService;

    @ApolloJsonValue("${juneyaoair.shuttleAirportList:[KIX]}")
    private List<String> shuttleAirportList;

    @Autowired
    private CacheService localCacheService;

    @Override
    public List<AirTransferFlightInfo> queryFlightInfo(AirTransferFlightQuery airTransferFlightQuery) {
        List<AirTransferFlightInfo> airTransferFlightInfoList = Lists.newArrayList();
        // 查询航班信息
        FlightInfoReqDTO flightInfoQuery = new FlightInfoReqDTO();
        flightInfoQuery.setFlightDate(airTransferFlightQuery.getFlightDate());
        flightInfoQuery.setFlightNo(airTransferFlightQuery.getFlightNo());
        flightInfoQuery.setDepCity(airTransferFlightQuery.getDepCity());
        flightInfoQuery.setArrCity(airTransferFlightQuery.getArrCity());
        BaseRequestDTO<FlightInfoReqDTO> baseRequest = BaseRequestUtil.createRequest(flightInfoQuery, ChannelCodeEnum.MOBILE.getChannelCode());
        List<FlightInfoDTO> flightInfoList = basicService.searchFlightInfo(baseRequest);
        if (CollectionUtils.isEmpty(flightInfoList)) {
            return airTransferFlightInfoList;
        }
        // 整合返回数据
        for (FlightInfoDTO flightInfo : flightInfoList) {
            AirTransferFlightInfo airTransferFlightInfo = new AirTransferFlightInfo();
            airTransferFlightInfo.setFlightNo(flightInfo.getFlightNo());
            airTransferFlightInfo.setFlightDate(flightInfo.getFlightDate());
            airTransferFlightInfo.setDepAirportCode(flightInfo.getDepAirport());
            airTransferFlightInfo.setArrAirportCode(flightInfo.getArrAirport());
            airTransferFlightInfo.setDepCityCode(flightInfo.getDepCity());
            airTransferFlightInfo.setArrCityCode(flightInfo.getArrCity());
            String planeTypeName = basicService.getPlaneTypeName(flightInfo.getPlanType(), SecurityContextHolder.getLanguage().name());
            airTransferFlightInfo.setPlaneTypeName(planeTypeName);
            airTransferFlightInfo.setDepDateTime(DateUtil.timeFormat(flightInfo.getDepDateTime()));
            airTransferFlightInfo.setArrDateTime(DateUtil.timeFormat(flightInfo.getArrDateTime()));
            airTransferFlightInfo.setDepAirportTerminal(StringUtils.isBlank(flightInfo.getDepAirportTerminal()) ? "" : flightInfo.getDepAirportTerminal());
            airTransferFlightInfo.setArrAirportTerminal(StringUtils.isBlank(flightInfo.getArrAirportTerminal()) ? "" : flightInfo.getArrAirportTerminal());
            // 计算飞行时长
            int flyTime = DateUtil.dateminuteDiff(DateUtil.toAllDate(flightInfo.getArrDateChinaTime()), DateUtil.toAllDate(flightInfo.getDepDateChinaTime()));
            airTransferFlightInfo.setDuration(flyTime < 0 ? null : flyTime);
            // 设置机场名、城市名称
            ApiAirPortInfoDto depAirPortInfo = cacheService.getLocalAirport(flightInfo.getDepAirport());
            ApiAirPortInfoDto arrAirportInfo = cacheService.getLocalAirport(flightInfo.getArrAirport());
            airTransferFlightInfo.setDepCityName(depAirPortInfo.getCityName());
            airTransferFlightInfo.setArrCityName(arrAirportInfo.getCityName());
            airTransferFlightInfo.setDepAirportName(depAirPortInfo.getAirPortName());
            airTransferFlightInfo.setArrAirportName(arrAirportInfo.getAirPortName());
            AirCompany airCompany = searchBffConfig.getAirCompanyMap().get("HO");
            airTransferFlightInfo.setAirIcon(null == airCompany ? null : airCompany.getAirIcon());
            airTransferFlightInfo.setIsInn(shuttleAirportList.contains(flightInfo.getArrAirport()));
            Segment segmentInfo = localCacheService.getSegment(flightInfo.getDepCity(), flightInfo.getArrCity());
            airTransferFlightInfo.setAirlineType(segmentInfo.getSegmentType());
            airTransferFlightInfoList.add(airTransferFlightInfo);
        }
        return airTransferFlightInfoList;
    }
}
