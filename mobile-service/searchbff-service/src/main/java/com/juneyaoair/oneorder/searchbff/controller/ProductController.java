package com.juneyaoair.oneorder.searchbff.controller;

import com.alibaba.fastjson.JSON;
import com.juneyaoair.oneorder.dto.AvailableInMile.AvailableInMileReqDto;
import com.juneyaoair.oneorder.dto.AvailableInMile.AvailableInMileResDto;
import com.juneyaoair.oneorder.dto.QueryAvailable.ProductAvailableReqDto;
import com.juneyaoair.oneorder.dto.QueryAvailable.ProductAvailableResDto;
import com.juneyaoair.oneorder.dto.QueryAvailable.ProductIdAvailableResDto;
import com.juneyaoair.oneorder.dto.orderDetail.ProductOrderDetailReqDto;
import com.juneyaoair.oneorder.dto.orderDetail.ProductOrderDetailResDto;
import com.juneyaoair.oneorder.fegin.ProductOrderClient;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 运价Controller
 *
 * <AUTHOR>
 * @date 2023/4/18 15:12
 */
@Slf4j
@Api(value = "ProductController")
@RestController
public class ProductController {

    @Resource
    private ProductOrderClient productOrderClient;


    @PostMapping("/productQueryAvailable")
    public ResponseData<ProductAvailableResDto> productQueryAvailable(@RequestBody RequestData<ProductAvailableReqDto> req) {
        log.info(this.getClass().getName() + "-productQueryAvailable-请求入参req={}", JSON.toJSONString(req));
        return productOrderClient.productQueryAvailable(req);
    }

    @PostMapping("/queryProductByProductId")
    public ResponseData<ProductIdAvailableResDto> queryProductByProductId(@RequestBody RequestData<String> req) {
        log.info(this.getClass().getName() + "-queryProductByProductId-请求入参req={}", JSON.toJSONString(req));
        return productOrderClient.queryProductByProductId(req);
    }

    @PostMapping("/queryProductByDisneyPlu")
    public ResponseData<ProductIdAvailableResDto> queryProductByDisneyPlu(@RequestBody RequestData<String> req) {
        log.info(this.getClass().getName() + "-queryProductByDisneyPlu-请求入参req={}", JSON.toJSONString(req));
        return productOrderClient.queryProductByDisneyPlu(req);
    }

    @PostMapping("/getProductAvailableInMile")
    public ResponseData<List<AvailableInMileResDto>> getProductAvailableInMile(@RequestBody RequestData<AvailableInMileReqDto> req) {
        log.info(this.getClass().getName() + "-getProductAvailableInMile-请求入参req={}", JSON.toJSONString(req));
        return productOrderClient.getProductAvailableInMile(req);
    }


    @PostMapping("/productOrderGetDetail")
    public ResponseData<ProductOrderDetailResDto> productOrderGetDetail(@RequestBody RequestData<ProductOrderDetailReqDto> req) {
        log.info(this.getClass().getName() + "-productOrderGetDetail-请求入参req={}", JSON.toJSONString(req));
        return productOrderClient.productOrderGetDetail(req);
    }


}
