package com.juneyaoair.oneorder.searchbff.service;

import com.juneyaoair.oneorder.airtransfer.AirTransferFlightInfo;
import com.juneyaoair.oneorder.airtransfer.AirTransferFlightQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 接送机
 * @created 2024/3/28 9:23
 */
public interface AirTransferService {

    /**
     * 接送机航班查询
     * @param airTransferFlightQuery
     * @return
     */
    List<AirTransferFlightInfo> queryFlightInfo(AirTransferFlightQuery airTransferFlightQuery);
}
