package com.juneyaoair.oneorder.searchbff;

import org.testng.Assert;

/**
 * <AUTHOR>
 * @date 2024/3/8 10:27
 */
public class TongDunPermitTest {

    public static final String tongDunPermitChannelCode = "HOCAR";

    public static void main(String[] args) {
        boolean hocar = ifChannelCodePermit("HOCAR");
        Assert.assertTrue(hocar);
    }



    /**
     * 指定渠道号放行同盾，true表示放行，false表示要走同盾
     *
     * @return
     */
    public static   boolean ifChannelCodePermit(String channelNo) {
        if (tongDunPermitChannelCode.contains(channelNo)) {
            return true;
        }
        return false;
    }
}
