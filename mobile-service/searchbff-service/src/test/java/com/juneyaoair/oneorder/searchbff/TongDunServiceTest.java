package com.juneyaoair.oneorder.searchbff;

import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.common.service.TongDunService;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpServletRequest;

import javax.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(org.mockito.junit.jupiter.MockitoExtension.class)
public class TongDunServiceTest {

    private TongDunService tongDunService;

    private HttpServletRequest httpServletRequest;

    @BeforeEach
    public void setUp() {
        tongDunService = new TongDunService();
        httpServletRequest = new MockHttpServletRequest(); // 使用 Spring 提供的 Mock 请求对象

        // 设置默认配置值
        tongDunService.tongDunPermitIpPrefix0 = "172";
        tongDunService.httpServletRequest = httpServletRequest;
    }

    /**
     * TC01: 客户端 IP 为 **********，前缀匹配
     */
    @Test
    public void testIfIpPermit_PrefixMatch_ReturnsTrue() {
        try (MockedStatic<HoAirIpUtil> hoAirIpUtilMockedStatic = Mockito.mockStatic(HoAirIpUtil.class)) {
            hoAirIpUtilMockedStatic.when(() -> HoAirIpUtil.getIpAddr(httpServletRequest)).thenReturn("**********");

            boolean result = tongDunService.ifIpPermit();

            assertTrue(result);
        }
    }

    /**
     * TC02: 客户端 IP 为 ***********，前缀不匹配
     */
    @Test
    public void testIfIpPermit_PrefixNotMatch_ReturnsFalse() {
        try (MockedStatic<HoAirIpUtil> hoAirIpUtilMockedStatic = Mockito.mockStatic(HoAirIpUtil.class)) {
            hoAirIpUtilMockedStatic.when(() -> HoAirIpUtil.getIpAddr(httpServletRequest)).thenReturn("***********");

            boolean result = tongDunService.ifIpPermit();

            assertFalse(result);
        }
    }

    /**
     * TC03: 客户端 IP 为无效格式（无点号）
     */
    @Test
    public void testIfIpPermit_InvalidIpFormat_ReturnsFalse() {
        try (MockedStatic<HoAirIpUtil> hoAirIpUtilMockedStatic = Mockito.mockStatic(HoAirIpUtil.class)) {
            hoAirIpUtilMockedStatic.when(() -> HoAirIpUtil.getIpAddr(httpServletRequest)).thenReturn("1721601");

            boolean result = tongDunService.ifIpPermit();

            assertFalse(result);
        }
    }

    /**
     * TC04: 获取 IP 抛出异常
     */
    @Test
    public void testIfIpPermit_ExceptionThrown_ReturnsFalse() {
        try (MockedStatic<HoAirIpUtil> hoAirIpUtilMockedStatic = Mockito.mockStatic(HoAirIpUtil.class)) {
            hoAirIpUtilMockedStatic.when(() -> HoAirIpUtil.getIpAddr(httpServletRequest)).thenThrow(new RuntimeException("Mock Exception"));

            boolean result = tongDunService.ifIpPermit();

            assertFalse(result);
        }
    }
}