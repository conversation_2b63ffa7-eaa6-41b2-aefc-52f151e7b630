# 构建镜像，执行命令：【docker build -t call-coupon:2.0 .】
#FROM openjdk:8u212-jre
FROM harbor.hoair.cn/library/openjdk:8-jre
MAINTAINER Arber C.

# 时区问题
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

ADD target/searchbff-service.jar /app/app.jar

ENTRYPOINT ["java", "-server", "-Xms4096M", "-Xmx4096M", "-Dfile.encoding=UTF-8", "-XX:+HeapDumpOnOutOfMemoryError", "-jar", "/app/app.jar"]


