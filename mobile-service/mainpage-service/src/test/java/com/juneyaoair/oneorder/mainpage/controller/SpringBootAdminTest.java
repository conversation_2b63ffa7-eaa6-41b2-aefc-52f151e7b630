package com.juneyaoair.oneorder.mainpage.controller;

import com.alibaba.fastjson.JSONObject;
import com.juneyaoair.oneorder.order.util.Functions;
import org.junit.Test;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

public class SpringBootAdminTest {
    public static void main(String[] args) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);
        ResponseEntity<JSONObject> exchange = new RestTemplate().exchange("http://10.12.219.101:9000/applications/HORDER-SERVICE-CALLCENTER",
                HttpMethod.GET, httpEntity, JSONObject.class);
        System.out.println(exchange.getBody());
    }

    @Test
    public void testInstance() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> httpEntity = new HttpEntity<>(null, headers);
        ResponseEntity<JSONObject> exchange = new RestTemplate().exchange("http://10.12.219.101:9000/instances/54aa19a26f6c",
                HttpMethod.GET, httpEntity, JSONObject.class);
        System.out.println(exchange.getBody());
    }

    @Test
    public void testSha() {
        System.out.println(Functions.sha1Sign("018-2327456940"));
    }

}
