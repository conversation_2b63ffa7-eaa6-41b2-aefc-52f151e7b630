package com.juneyaoair.oneorder.mainpage.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.juneyaoair.oneorder.mainpage.MainPageServiceApplication;
import com.juneyaoair.oneorder.tools.utils.FileUtils;
import lombok.Data;
import org.checkerframework.checker.nullness.qual.AssertNonNullIfNonNull;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;

class HomeControllerTest {

    private String filePath = "/application_apollo.json";//apollo现有配置
    private String propertiesFile = "text3.properties";//新增配置

    private String updateMan = "qiguodong";//改动者

    private String updateTime = "Oct 31, 2023 16:24:37 PM";//改动时间

    @Test
    void queryMessageList() throws Exception {

        String s = FileUtils.readJson(filePath);
        List<ApolloClazz> oriList = JSONArray.parseArray(s, ApolloClazz.class);

        ArrayList<ApolloClazz> ret = new ArrayList<>();
        InputStreamReader in = new InputStreamReader(Objects.requireNonNull(MainPageServiceApplication.class
                .getClassLoader()
                .getResourceAsStream(propertiesFile)),
                StandardCharsets.UTF_8);
        Properties properties = new Properties();
        properties.load(in);
        int num = oriList.size();
        ArrayList<ApolloClazz> toAddList = new ArrayList<>();
        for (Map.Entry<Object, Object> entry : properties.entrySet()) {
            String key = (String) entry.getKey();
            String value = (String) entry.getValue();
            ApolloClazz apolloClazz = null;
            for (ApolloClazz clazz : oriList) {
                if (clazz.key == null || !clazz.key.equalsIgnoreCase(key)) {
                    continue;
                }
                apolloClazz = clazz;
                break;
            }
            if (apolloClazz == null) {
                ApolloClazz toAdd = new ApolloClazz();
                toAdd.id = 0;
                toAdd.namespaceId = 0;
                toAdd.key = key;
                toAdd.type = 0;
                toAdd.value = value;
                toAdd.lineNum = num++;
                toAdd.dataChangeCreatedBy = updateMan;
                toAdd.dataChangeLastModifiedBy = updateMan;
                toAdd.dataChangeCreatedByDisplayName = updateMan;
                toAdd.dataChangeLastModifiedByDisplayName = updateMan;
                toAdd.dataChangeCreatedTime = updateTime;
                toAdd.dataChangeLastModifiedTime = updateTime;
                toAddList.add(toAdd);
            } else {
                apolloClazz.value = value;
                apolloClazz.dataChangeLastModifiedBy = updateMan;
                apolloClazz.dataChangeLastModifiedTime = updateTime;
            }
        }
        ret.addAll(oriList);
        ret.addAll(toAddList);
        String jsonPrettyStr = JSONUtil.toJsonStr(ret);
        Assertions.assertNotNull(jsonPrettyStr);
        System.out.println(jsonPrettyStr);
    }

    @Data
    static class ApolloClazz {
        public int id;
        public int namespaceId;
        public String key;
        public int type;
        public String value;
        public String comment;
        public int lineNum;
        public String dataChangeCreatedBy;
        public String dataChangeLastModifiedBy;
        public String dataChangeCreatedByDisplayName;
        public String dataChangeLastModifiedByDisplayName;
        public String dataChangeCreatedTime;
        public String dataChangeLastModifiedTime;

    }
}