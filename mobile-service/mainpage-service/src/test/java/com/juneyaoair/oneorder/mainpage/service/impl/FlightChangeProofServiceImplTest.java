package com.juneyaoair.oneorder.mainpage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.concurrency.HOContext;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofResp;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofSendInfo;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@SpringBootTest
class FlightChangeProofServiceImplTest {

    @Resource
    HttpServletRequest request;
    @Resource
    HttpServletResponse response;
    @Resource
    private FlightChangeProofServiceImpl flightChangeProofService;
    @Test
    void sendFlightChangeProof() {
        HOContext.setContext(new HOContext());
        FlightChangeProofSendInfo proofSendInfo = new FlightChangeProofSendInfo();
        FlightChangeProofResp proofResp = new FlightChangeProofResp();
        proofResp.setStatusReason("流量控制");
        proofResp.setSegmentStatus("C");
        proofSendInfo.setProofResps(CollUtil.newArrayList(proofResp));
        RequestData<FlightChangeProofSendInfo> requestData = new RequestData<>();
        requestData.setData(proofSendInfo);
        requestData.setChannelNo("B2C");
        flightChangeProofService.sendFlightChangeProof(requestData,request,response);
    }
}