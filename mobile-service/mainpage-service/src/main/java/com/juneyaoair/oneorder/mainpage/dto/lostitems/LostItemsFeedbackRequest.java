package com.juneyaoair.oneorder.mainpage.dto.lostitems;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@ApiModel
@XmlRootElement(name = "LostItemsApplyRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class LostItemsFeedbackRequest extends LostItemsApplyRequest {
    @ApiModelProperty("航班号")
    @NotEmpty(message = "航班号")
    @Size(min = 6, max = 6, message = "航班号格式不对")
    private String rlFlightNo;
    @ApiModelProperty("航班日期")
    @NotEmpty(message = "航班日期")
    private String flightDate;
    @ApiModelProperty(value = "遗失地点",example = "不确定 1；前卖座椅口袋 2；上方行李架 3；座位上 4；洗手间 5；地面 6;厨房 7；贵宾室 8")
    @NotEmpty(message = "遗失地点")
    private String lossPlace;
    @ApiModelProperty("遗失物品类型")
    @NotEmpty(message = "遗失物品类型")
    private String leaveBehindLevel;

    @ApiModelProperty("遗失物品描述")
    public String description;//todo gdqi



    public String getLeaveBehindLevel() {
        return leaveBehindLevel;
    }

    public void setLeaveBehindLevel(String leaveBehindLevel) {
        this.leaveBehindLevel = leaveBehindLevel;
    }

    @ApiModelProperty()
    private String filePath;

    @ApiModelProperty()
    private List<String> fileUrls;

    public String getRlFlightNo() {
        return rlFlightNo;
    }

    public void setRlFlightNo(String rlFlightNo) {
        this.rlFlightNo = rlFlightNo;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getLossPlace() {
        return lossPlace;
    }

    public void setLossPlace(String lossPlace) {
        this.lossPlace = lossPlace;
    }


    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public List<String> getFileUrls() {
        return fileUrls;
    }

    public void setFileUrls(List<String> fileUrls) {
        this.fileUrls = fileUrls;
    }
}
