package com.juneyaoair.oneorder.mainpage.dto.tripcert;

import com.juneyaoair.oneorder.common.dto.GeetestDto;
import com.juneyaoair.oneorder.constant.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
@ApiModel
public class DeliveryTicketInfoUser extends GeetestDto {
    @ApiModelProperty("票号")
    @NotBlank(message = "客票信息不能为空")
    @Pattern(regexp = PatternCommon.TICKET_NO,message = "请输入正确的票号")
    private String ticketNo;
    @ApiModelProperty("乘机人姓名")
    @NotBlank(message = "乘机人姓名不能为空")
    private String passName;
    @ApiModelProperty("blackBox")
    private String blackBox;

    @ApiModelProperty("出发城市code")
    @NotNull(message = "出发城市不能为空")
    public String depCityCode;
    @ApiModelProperty("到达城市code")
    @NotNull(message = "到达城市不能为空")
    public String arrCityCode;
    @ApiModelProperty("航班号")
    @NotNull(message = "航班号不能为空")
    public String flightNo;
    @ApiModelProperty(value = "航班时间", example = "2024-01-01")
    @NotNull(message = "航班时间不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式需为yyyy-MM-dd")
    public String flightDate;


}
