package com.juneyaoair.oneorder.mainpage.util;


import com.juneyaoair.oneorder.order.constant.CertificateTypeEnum;
import com.juneyaoair.oneorder.order.dto.IdentityInfo;

import java.util.Arrays;
import java.util.List;

/**
 * IBE证件处理工具类
 */
public class IdentityInfoUtil {
    /**
     * 从证件列表中获取相关证件信息
     * @param identityInfoList
     * @return
     */
    public static IdentityInfo getIdentityInfo(List<IdentityInfo> identityInfoList){
        //去除已知不是代表证件的类型 FF-会员卡号 PH-国际手机号 RP-电子行程单 CC-可能不一定代表其他证件
        List<String> notIdentityInfoList = Arrays.asList("FF","PH","RP");
        identityInfoList.removeIf(identityInfo -> notIdentityInfoList.contains(identityInfo.getIdType()));

        IdentityInfo identityInfo = identityInfoList.stream().filter(temp -> CertificateTypeEnum.getShowCodeListExcludeCC()
                .contains(temp.getIdType()) || "UU".equals(temp.getIdType()))
                .findFirst().orElse(null);

        if(identityInfo == null){
            identityInfo = identityInfoList.stream().filter(temp -> "ID".equals(temp.getIdType()))
                    .findFirst().orElse(null);
        }
        //保证CC其他证件最后获取
        if(identityInfo == null){
            identityInfo = identityInfoList.stream().filter(temp -> "CC".equals(temp.getIdType()))
                    .findFirst().orElse(null);
        }
        //保底证件信息获取
        if(identityInfo == null){
            identityInfo = identityInfoList.get(0);
        }
        return identityInfo;
    }
}
