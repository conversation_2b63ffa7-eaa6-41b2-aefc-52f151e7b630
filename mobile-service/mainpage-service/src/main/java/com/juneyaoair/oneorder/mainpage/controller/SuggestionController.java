package com.juneyaoair.oneorder.mainpage.controller;

import com.juneyaoair.oneorder.mainpage.dto.suggestion.ExpressViewsRequest;
import com.juneyaoair.oneorder.mainpage.service.ISuggestionService;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/suggestion")
@Api(value = "服务反馈", tags = "服务反馈")
public class SuggestionController {

    @Resource
    ISuggestionService suggestionService;

    @ApiOperation(value = "意见反馈", notes = "意见反馈")
    @RequestMapping(value = "expressViews", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public ResponseData<Boolean> expressViews(@RequestBody RequestData<ExpressViewsRequest> req, HttpServletRequest request,BindingResult bindingResult) {
        return suggestionService.expressViews(req, request, bindingResult);
    }

}
