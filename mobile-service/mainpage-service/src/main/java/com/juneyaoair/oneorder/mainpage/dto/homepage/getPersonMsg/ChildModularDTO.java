package com.juneyaoair.oneorder.mainpage.dto.homepage.getPersonMsg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description  服务相关配置
 */
@ApiModel(description = "服务相关配置")
@Data
public class ChildModularDTO {
    @ApiModelProperty()
    private String recordId;
    @ApiModelProperty()
    private String name;
    @ApiModelProperty("副标题")
    private String subTitle;//副标题
    @ApiModelProperty()
    private Integer orderNo;
    @ApiModelProperty("是否展示new标记")
    private boolean showNew;//是否展示new标记
    @ApiModelProperty("页面跳转地址")
    private String url;//页面跳转地址
    @ApiModelProperty()
    private String iconUrl;
    @ApiModelProperty()
    private String eventModule;
    @ApiModelProperty()
    private String eventType;
    @ApiModelProperty()
    private String winName;
    @ApiModelProperty("渠道")
    private String channelCode;//渠道
    @ApiModelProperty("是否需要登录")
    private String loginFlag;//是否需要登录
}
