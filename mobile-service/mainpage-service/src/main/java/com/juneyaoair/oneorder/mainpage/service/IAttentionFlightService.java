package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.commondto.ResponseData;
import com.juneyaoair.flightbasic.request.airLine.AttentionFlightParam;
import com.juneyaoair.flightbasic.request.airLine.CancelAttentionFlightParam;
import com.juneyaoair.flightbasic.request.airLine.QueryAttentionFlightParam;
import com.juneyaoair.oneorder.api.dsop.dto.FlightInfoDynamicDto;
import com.juneyaoair.oneorder.common.dto.BizDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/9 17:32
 */
public interface IAttentionFlightService {
    /**
     * 查询关注航班列表
     * @param ffpId
     * @param ffpCardNo
     * @param bizDto
     * @return
     */
    List<FlightInfoDynamicDto> queryAttentionFlightList(String ffpId, String ffpCardNo, BizDto bizDto);

    /**
     * 新增关注航班
     * @param requestData
     * @return
     */
    ResponseData addAttentionFlight(RequestData<AttentionFlightParam> requestData);

    /**
     * 取消航班关注
     * @param requestData
     * @return
     */
    ResponseData cancelAttentionFlight(RequestData<CancelAttentionFlightParam> requestData);
}
