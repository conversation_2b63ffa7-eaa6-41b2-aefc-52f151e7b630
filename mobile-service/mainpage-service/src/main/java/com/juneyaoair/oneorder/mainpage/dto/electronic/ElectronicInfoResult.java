package com.juneyaoair.oneorder.mainpage.dto.electronic;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description 开具结果结果
 * @created 2024/5/13 15:59
 */
@Data
public class ElectronicInfoResult {

    @ApiModelProperty("渠道订单编号")
    private String channelOrderNo;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("订单状态")
    private String orderState;

    @ApiModelProperty("支付状态")
    private String payState;

    @ApiModelProperty("乘客姓名")
    private String passengerName;

    @ApiModelProperty("乘客类型")
    private String passengerType;

    @ApiModelProperty("票号")
    private String ticketNo;

    @ApiModelProperty("发票号码")
    private String invoiceNumber;

    @ApiModelProperty("开票状态  Y可开具 N不可")
    private String invoiceState;

    @ApiModelProperty("发票状态  not-未开票 apply-申请 invoiced-已开据 credit-红冲 fail-失败")
    private String invoiceStatus;

    @ApiModelProperty("发票金额")
    private String invoiceAmount;

    @ApiModelProperty("电子邮箱")
    private String companyMail;

    @ApiModelProperty("发票产品：国内机票款，退票费，国内机票款，逾重行李券，升舱券")
    private String companyContent;

    @ApiModelProperty("冲红次数")
    private Integer redCount;

    @ApiModelProperty("航班日期")
    private String flightDate;

    @ApiModelProperty("行程")
    private String flightRute;

    @ApiModelProperty("签名")
    private String signature;

}
