package com.juneyaoair.oneorder.mainpage.dto.tripcert;

import com.juneyaoair.oneorder.constant.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;

/**
 * @description 邮寄地址信息
 */
@ApiModel(description = "邮寄地址信息")
@Data
public class DeliveryAddress {
    /**
     * 记录ID
     */
    @ApiModelProperty("记录ID")
    private String recordId;
    /**
     * 收件人
     */
    @ApiModelProperty("收件人")
    @NotBlank(message = "收件人信息不能为空")
    private String linker;
    /**
     * 手机
     */
    @ApiModelProperty(value = "手机", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = PatternCommon.MOBILE_PHONE,message = "手机号格式不正确")
    private String handphoneNumber;
    /**
     * 省份代码
     */
    @ApiModelProperty("省份代码")
    private String deliverToProvince;
    @ApiModelProperty(hidden = true)
    private String deliverToProvinceName;
    /**
     * 城市代码
     */
    @ApiModelProperty("城市代码")
    private String deliverToCityCode;
    @ApiModelProperty(hidden = true)
    private String deliverToCityName;
    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    @NotBlank(message = "详细地址不能为空")
    private String detailAddress;
    //地址类型 详见PostAddressTypeEnum
    @ApiModelProperty("地址类型 详见PostAddressTypeEnum")
    private Integer addressType;
}
