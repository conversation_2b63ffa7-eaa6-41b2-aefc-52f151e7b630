package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.oneorder.api.dsop.dto.FlightInfoDynamicDto;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.flight.dto.FlightDynamicParam;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/12 19:29
 */
public interface IFlightService {
    /**
     * 航班动态处理
     * @return
     */
    List<FlightInfoDynamicDto> searchFlightDynamicsInfo(String ffpId,String ffpCardNo,FlightDynamicParam flightDynamicParam);

    /**
     * 航班动态参数完善
     * @param flightDynamicParam
     * @param flightInfoDynamicDto
     */
    void supplyFlightInfoDynamicDto(BizDto flightDynamicParam, FlightInfoDynamicDto flightInfoDynamicDto);
}
