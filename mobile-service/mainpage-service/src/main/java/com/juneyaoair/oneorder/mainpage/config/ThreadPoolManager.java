package com.juneyaoair.oneorder.mainpage.config;


import com.juneyaoair.oneorder.common.concurrency.ThreadPool;
import com.juneyaoair.oneorder.common.concurrency.ThreadPoolImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class ThreadPoolManager {


    @Bean
    public ThreadPool threadPool(TaskDecorator taskDecorator) {
        return createThreadPool(
                10/*corePoolSize*/,
                100/*maxPoolSize*/,
                1000/*waitingQSize*/,
                taskDecorator);
    }

    private ThreadPool createThreadPool(int corePoolSize, int maxPoolSize, int waitingQSize, TaskDecorator taskDecorator) {
        return new ThreadPoolImpl(corePoolSize, maxPoolSize, waitingQSize, taskDecorator);
    }
}
