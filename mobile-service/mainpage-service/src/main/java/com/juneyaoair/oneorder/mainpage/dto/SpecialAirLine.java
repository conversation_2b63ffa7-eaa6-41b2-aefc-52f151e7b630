package com.juneyaoair.oneorder.mainpage.dto;

import com.juneyaoair.flightbasic.request.wechat.PicInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/8 10:53
 */
@Data
@ApiModel(value = "SpecialAirLine",description = "特惠航线DTO")
public class SpecialAirLine {
    @ApiModelProperty(value = "出发城市名称")
    private String depCity;
    @ApiModelProperty(value = "出发城市三字码")
    private String depCityCode;
    @ApiModelProperty(value = "出发城市国内国际地址标识",allowableValues = "N,I",notes = "N-国内航线，I-国际/地区航线")
    private String depCityIsInter;
    @ApiModelProperty(value = "到达城市名称")
    private String arrCity;
    @ApiModelProperty(value = "到达城市三字码")
    private String arrCityCode;
    @ApiModelProperty(value = "到达城市国内国际地址标识",allowableValues = "N,I",notes = "N-国内航线，I-国际/地区航线")
    private String arrCityIsInter;
    @ApiModelProperty(value = "低价日期",notes = "格式:yyyy-MM-dd")
    private String activeTime;
    @ApiModelProperty(value = "起始低价")
    private Double lowPrice;
    @ApiModelProperty(value = "移动端图片")
    private  String picture;
    @ApiModelProperty(value = "PC端图片")
    private  String picturePc;
    @ApiModelProperty(value = "图片信息清单")
    private List<PicInfo> picInfoList;
}
