package com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf;

import java.util.List;

public class GetedDataContext {
    public String template;
    public String segmentTemplate;
    public String fareCompute;
    public String payMethod;
    //fare 票价
    public String fare;
    //total 总价
    public String totalAmount;
    /**税费**/
    public String tax;
    public String issueDate;

    public List<Segment> segmentInfos;
    public StringBuilder certNo;
}
