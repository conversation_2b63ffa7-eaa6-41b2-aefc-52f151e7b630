package com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf;

import com.juneyaoair.oneorder.mainpage.constant.MainPageConstant;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Optional;

@Component
public class ItineraryThymeleafJpProcessor extends AbstractThymeleafProcessor {

    @Override
    public void prepareDiffData(ItineraryContext context) {
        context.getedDataContext.payMethod = context.flightItineraryInfo.getPayMethod();
        Date date = DateUtil.toDate(context.flightItineraryInfo.getIssueDate(), DateUtil.YYYY_MM_DD_PATTERN);
        SimpleDateFormat dateFormat = new SimpleDateFormat("ddMMMyy", Locale.UK);
        context.getedDataContext.issueDate = dateFormat.format(date);
        //英文版本的电子邮件
        context.getedDataContext.template = MainPageConstant.TRAVEL_ITINERARY_EMAIL_TEMPLATE_JP;
    }

    @Override
    protected String templateProcess(ItineraryContext context) {
        Context thyContext = new Context();
        thyContext.setVariable("pnr", context.flightItineraryInfo.getDetrSegmentDetailList().get(0).getPnrNo() == null ? "" : context.flightItineraryInfo.getDetrSegmentDetailList().get(0).getPnrNo());
        thyContext.setVariable("passengerName", context.flightItineraryInfo.getPassengerName());
        thyContext.setVariable("ticketNo", StringUtils.isBlank(context.flightItineraryInfo.getFollowTicketNo()) ? context.flightItineraryInfo.getTicketNo() : "");
        thyContext.setVariable("followTicketNo", context.flightItineraryInfo.getFollowTicketNo());
        thyContext.setVariable("certNo", context.getedDataContext.certNo);
        thyContext.setVariable("issueDate", context.getedDataContext.issueDate);
        thyContext.setVariable("iataNo", context.flightItineraryInfo.getIataNo());
        thyContext.setVariable("signingInfo", Optional.ofNullable(context.flightItineraryInfo.getSigningInfo()).orElse(""));
        thyContext.setVariable("segmentInfos", context.getedDataContext.segmentInfos);
        thyContext.setVariable("fare", context.getedDataContext.fare);
        thyContext.setVariable("fareCompute", context.getedDataContext.fareCompute);
        thyContext.setVariable("totalAmount", context.getedDataContext.totalAmount);
        thyContext.setVariable("payMethod", context.getedDataContext.payMethod);
        thyContext.setVariable("englishTax", context.getedDataContext.tax);
        thyContext.setVariable("hoLogo",context.hoLogo);
        thyContext.setVariable("iataLogo",context.iataLogo);
        return templateEngine.process(context.getedDataContext.template, thyContext);
    }
}
