package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.flightbasic.request.notice.NoticeRequestDTO;
import com.juneyaoair.flightbasic.response.notice.NoticeInfoResponseDTO;
import com.juneyaoair.flightbasic.response.notice.TNoticeInfoResponseDTO;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.policy.NoticeInfo;
import com.juneyaoair.oneorder.policy.NoticeText;
import com.juneyaoair.oneorder.policy.PolicyModule;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/30 13:28
 */
@Slf4j
@Service
public class ProvisionServiceAggr {

    @Autowired
    private IBasicService basicService;

    public List<PolicyModule> getAllNoticeInfo(BizDto bizDto, LanguageEnum language, NoticeRequestDTO noticeRequestDTO) {
        if(StringUtils.isBlank(noticeRequestDTO.getDictTypeCode())){
            throw new ArgumentCheckFailException("参数不完整");
        }
        // 语言不为中文 协议编码为 入参编码+语言
        if (!LanguageEnum.ZH_CN.equals(language)) {
            noticeRequestDTO.setDictTypeCode(noticeRequestDTO.getDictTypeCode() + "_" + language.name());
        }
        List<NoticeInfoResponseDTO> noticeInfoResponseDTOList = basicService.getAllNoticeInfo(bizDto, noticeRequestDTO);
        List<PolicyModule> policyModuleList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(noticeInfoResponseDTOList)) {
            noticeInfoResponseDTOList.forEach(noticeInfoResponseDTO -> {
                if (CollectionUtils.isNotEmpty(noticeInfoResponseDTO.getNoticeResponseDTOs())) {
                    noticeInfoResponseDTO.getNoticeResponseDTOs().forEach(notice -> {
                        PolicyModule policyModule = new PolicyModule();
                        policyModule.setModuleName(notice.getNtName());
                        policyModule.setOrderNum(NumberUtils.toInt(notice.getNtCode(), 0));
                        List<NoticeInfo> noticeInfoList = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(notice.getNoticeInfo())) {
                            notice.getNoticeInfo().forEach(noticeInfo -> {
                                NoticeInfo policy = new NoticeInfo();
                                policy.setNtInfoId(noticeInfo.getNtInfoId());
                                policy.setOrderNum(NumberUtils.toInt(noticeInfo.getNtInfoCode(), 0));
                                policy.setTitle(noticeInfo.getNtInfoName());
                                policy.setUrl(noticeInfo.getNtInfoUrl());
                                policy.setIconUrl("https://mediaws.juneyaoair.com/upload/icon/yaolan.png");
                                Date date = DateUtil.toDate(noticeInfo.getModifyTime(), DateUtil.YYYY_MM_DD_HH_MM_SS_PATTERN);
                                if (date!=null) {
                                    policy.setUpdateTimestamp(date.getTime());
                                }
                                noticeInfoList.add(policy);
                            });
                        }
                        policyModule.setNoticeInfoList(noticeInfoList);
                        policyModuleList.add(policyModule);
                    });
                }
            });
        }
        return policyModuleList;
    }

    public NoticeText getRichTextNoticeInfo(BizDto bizDto, NoticeRequestDTO noticeRequestDTO) {
        if(StringUtils.isBlank(noticeRequestDTO.getNtInfoId())){
            throw new ArgumentCheckFailException("参数不完整");
        }
        TNoticeInfoResponseDTO noticeInfoResponseDTO = basicService.getRichTextNoticeInfo(bizDto,noticeRequestDTO);
        NoticeText noticeText = new NoticeText();
        noticeText.setNtTitle(noticeInfoResponseDTO.getNtInfoName());
        try{
            noticeText.setNtText(new String(noticeInfoResponseDTO.getRichText(), "GBK"));
        }catch (UnsupportedEncodingException unsupportedEncodingException){
            log.error("{}字符编码转换异常",noticeRequestDTO.getNtInfoId(),unsupportedEncodingException);
        }
        return noticeText;
    }
}
