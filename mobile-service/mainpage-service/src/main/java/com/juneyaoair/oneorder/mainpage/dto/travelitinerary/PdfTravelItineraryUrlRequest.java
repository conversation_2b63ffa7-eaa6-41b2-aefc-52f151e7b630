package com.juneyaoair.oneorder.mainpage.dto.travelitinerary;

import com.juneyaoair.oneorder.common.dto.GeetestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Data
public class PdfTravelItineraryUrlRequest extends GeetestDto {
    @ApiModelProperty("票号")
    @NotNull(message="票号不能为空")
    private String ticketNo;
    @ApiModelProperty("旅客姓名")
    @NotNull(message="旅客姓名不能为空")
    private String passengerName;

    @ApiModelProperty("出发城市code")
    @NotNull(message = "出发城市不能为空")
    public String depCityCode;
    @ApiModelProperty("到达城市code")
    @NotNull(message = "到达城市不能为空")
    public String arrCityCode;
    @ApiModelProperty("航班号")
    @NotNull(message = "航班号不能为空")
    public String flightNo;
    @ApiModelProperty(value = "航班时间", example = "2024-01-01")
    @NotNull(message = "航班时间不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "日期格式需为yyyy-MM-dd")
    public String flightDate;
}
