package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.mainpage.dto.SpecialAirLine;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description  特价航线
 * @date 2023/6/8 15:57
 */
public interface ISpecialAirLineService {
    /**
     * 查询特价航线缓存
     * @return
     */
    List<SpecialAirLine> querySpecialAirlinePrice(BizDto bizDto, String currency, @NotNull LanguageEnum language);
}
