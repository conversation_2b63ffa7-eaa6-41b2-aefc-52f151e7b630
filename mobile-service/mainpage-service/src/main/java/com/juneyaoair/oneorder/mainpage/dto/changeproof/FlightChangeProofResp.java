package com.juneyaoair.oneorder.mainpage.dto.changeproof;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@ApiModel
@AllArgsConstructor
@NoArgsConstructor
@Data
public class FlightChangeProofResp {
    /**
     * 终点城市
     */
    @ApiModelProperty("终点城市")
    private String DstCity;
    /**
     * 终点英文
     */
    @ApiModelProperty("终点英文")
    private String DstEnCity;
    /**
     * 起点城市
     */
    @ApiModelProperty("起点城市")
    private String OrgCity;
    /**
     * 起点英文
     */
    @ApiModelProperty("起点英文")
    private String OrgEnCity;
    /**
     * 航班编号
     */
    @ApiModelProperty("航班编号")
    private String FlightNo;
    /**
     * 计划起飞时间
     */
    @ApiModelProperty("计划起飞时间")
    private String DepTime;
    /**
     * 计划起飞时间 hh:mm
     */
    @ApiModelProperty("计划起飞时间 hh:mm")
    private String DepSpecificTime;
    /**
     * 计划到达时间
     */
    @ApiModelProperty("计划到达时间")
    private String ArrTime;
    /**
     * 计划到达时间 hh：mm
     */
    @ApiModelProperty("计划到达时间 hh：mm")
    private String ArrSpecificTime;
    /**
     * 实际起飞时间
     */
    @ApiModelProperty("实际起飞时间")
    private String ActDepTime;
    /**
     * 实际到达时间
     */
    @ApiModelProperty("实际到达时间")
    private String ActArrTime;
    /**
     * 航班状态 C:取消  ，D：延误
     */
    @ApiModelProperty("航班状态 C:取消  ，D：延误")
    private String SegmentStatus;
    /**
     * 航班状态原因原因
     */
    @ApiModelProperty("航班状态原因原因")
    private String StatusReason;
    /**
     * 到达航站楼
     */
    @ApiModelProperty("到达航站楼")
    private String ArrAirportTerminal;
    /**
     * 起飞航站楼
     */
    @ApiModelProperty("起飞航站楼")
    private String DepAirportTerminal;
    /**
     * 到达机场代码
     */
    @ApiModelProperty("到达机场代码")
    private String ArrAirportCode;
    /**
     * 起飞机场代码
     */
    @ApiModelProperty("起飞机场代码")
    private String DepAirportCode;
    /**
     * 到达机场中文起飞机场中文
     */
    @ApiModelProperty("到达机场中文起飞机场中文")
    private String ArrAirportName;
    /**
     *出发机场中文起飞机场中文
     */
    @ApiModelProperty("出发机场中文起飞机场中文")
    private String DepAirportName;
    /**
     * 飞行时长
     */
    @ApiModelProperty("飞行时长")
    private Long FlightTime;
    /**
     * 跨天数
     */
    @ApiModelProperty("跨天数")
    private int day;
    /**
     * 旅客姓名
     */
    @ApiModelProperty("旅客姓名")
    private String PassengerName;
    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    private String FlightDate;
    /**
     * 航班日期yyyy-MM-dd
     */
    @ApiModelProperty("航班日期yyyy-MM-dd")
    private String FlightDateTime;
    /**
     * 周几
     */
    @ApiModelProperty("周几")
    private String weekStr;
    /**
     * 身份证
     */
    @ApiModelProperty("身份证")
    private String IdNo;
    /**
     * 身份证类型
     */
    @ApiModelProperty("身份证类型")
    private String IdType;
    /**
     * 航班延误证明编号
     */
    @ApiModelProperty("航班延误证明编号")
    private String delayNum;

    /**
     * 离港延误标记 2020-10-30
     */
    @ApiModelProperty("离港延误标记 2020-10-30")
    private Boolean DepDelay;
    /*
     * 到港延误标记 2020-10-30
     */
    @ApiModelProperty("到港延误标记 2020-10-30")
    private Boolean ArrDelay;
    /**
     * 出发城市时区 2020-11-03
     */
    @ApiModelProperty("出发城市时区 2020-11-03")
    private Integer DepCityZone;
    /**
     * 到达城市时区 2020-11-03
     */
    @ApiModelProperty("到达城市时区 2020-11-03")
    private Integer ArrCityZone;


    /**
     * 滑入时间
     */
    @ApiModelProperty("滑入时间")
    public String flightInn;

    /**
     * 滑出时间
     */
    @ApiModelProperty("滑出时间")
    public String flightOut;
}
