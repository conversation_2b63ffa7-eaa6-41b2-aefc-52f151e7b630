package com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class Segment {
    @ApiModelProperty(value = "出发机场名称",notes = "自行和城市名称拼接")
    public String depAirport;
    @ApiModelProperty(value = "到达机场名称",notes = "自行和城市名称拼接")
    public String arrAirport;
    public String flightNo;
    public String cabinCode;
    public String flightDate;
    public String depTime;
    public String arrTime;
    public String segmentStatus;
    public String depAirportTerminal;
    public String arrAirportTerminal;
    public String days;
    public String baggageWeight;
}
