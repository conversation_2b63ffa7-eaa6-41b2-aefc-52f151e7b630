package com.juneyaoair.oneorder.mainpage.service.impl;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.request.commonFlyCity.CommonFlyCityReqDTO;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.flightbasic.response.commonFlyCity.CommonFlyCityDetailDTO;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.RouteTypeEnum;
import com.juneyaoair.oneorder.mainpage.dto.cfc.CFCModuleRequest;
import com.juneyaoair.oneorder.mainpage.dto.cfc.CommonFlyCityBO;
import com.juneyaoair.oneorder.mainpage.dto.cfc.NearFlightRecommend;
import com.juneyaoair.oneorder.mainpage.dto.cfc.ResultJson;
import com.juneyaoair.oneorder.mainpage.service.IFlightFareService;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.utils.Redis0Util;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.tools.enums.EnumInterNationTag;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@Service
public class FlightFareServiceImpl implements IFlightFareService {

    @Resource
    CacheService cacheService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private Redis0Util redis0Util;
    @Autowired
    private RedisConstantConfig redisConstantConfig;
    @Autowired
    private IBasicService basicService;
    @Resource
    private FlightBasicConsumerClient flightBasicConsumerClient;
    /**
     * 邻近航线
     */
    private static final String SOLD_OUT_PAGE_MODULE_NEARBY = "1";
    /**
     * 热门推荐
     */
    private static final String SOLD_OUT_PAGE_MODULE_RECOMMENDATION = "2";


    /**
     * 查询 共飞城市"邻近航线、热门推荐“类型数据
     * <p>
     * "邻近航线":  到达地相同，出发地在【邻近航线】共飞组内 （出发地邻近）
     * <p>
     * "热门推荐":  出发地相同，目的地在【热门推荐】共飞组内
     *
     * @param request
     * @return
     */
    @Override
    public List<NearFlightRecommend> getCFCAirRoute(RequestDataDto<CFCModuleRequest> request) {
        try {
            List<NearFlightRecommend> cfcModuleList = new ArrayList<>();
            // 邻近航线模块还是热门推荐模块
            NearFlightRecommend cfcNearBy = getCFCModuleByRuleType(request, SOLD_OUT_PAGE_MODULE_NEARBY);
            NearFlightRecommend cfcRecommendation = getCFCModuleByRuleType(request, SOLD_OUT_PAGE_MODULE_RECOMMENDATION);
            // 合并数据
            if (cfcNearBy != null) {
                cfcModuleList.add(cfcNearBy);
            }
            if (cfcRecommendation != null) {
                cfcModuleList.add(cfcRecommendation);
            }
            return cfcModuleList;
        } catch (Exception e) {
            log.error("queryPopularRecommendationFlight error", e);
        }
        return new ArrayList<>();
    }

    private NearFlightRecommend getCFCModuleByRuleType(RequestDataDto<CFCModuleRequest> request, String ruleType) {
        List<CommonFlyCityDetailDTO> nearByRouteOrRecommendation = new ArrayList<>();
        String key;
        boolean isSetToRedis = true;
        boolean isNearby = SOLD_OUT_PAGE_MODULE_NEARBY.equals(ruleType);
        CFCModuleRequest cfcRequest = request.getData();
        String currency = request.getCurrency();
        //
        ApiCityInfoDto apiCityInfoDto = cacheService.getLocalCity(cfcRequest.getDepCode());
        String tripType = apiCityInfoDto.getIsInternational();
        //
        // 从redis中获取,如果没有则调用接口
        if (isNearby) {

            key = RedisConstantConfig.COMMON_FLYING_CITY + ":" + cfcRequest.getDepCode() + ":" + tripType;
        } else {
            key = RedisConstantConfig.COMMON_FLYING_CITY + ":" + cfcRequest.getArrCode() + ":" + tripType + ":" + "2";
        }
        // TRANSFORM TO API KEY
        key = redisConstantConfig.getAPIRedisKey(key);
        String redisResult = (String) redisUtil.get(key);
        if (StringUtil.isNullOrEmpty(redisResult)) {
            CommonFlyCityReqDTO cfcReq = new CommonFlyCityReqDTO();
            if (isNearby) {
                cfcReq.setRuleType(SOLD_OUT_PAGE_MODULE_NEARBY);
                cfcReq.setCityCode(cfcRequest.getDepCode());
            } else {
                cfcReq.setRuleType(SOLD_OUT_PAGE_MODULE_RECOMMENDATION);
                cfcReq.setCityCode(cfcRequest.getArrCode());
            }
            //
            String resJson = new Gson().toJson(flightBasicConsumerClient.queryCommonFlyCityDetailsByCityCode(cfcReq));
            ResultJson<List<CommonFlyCityDetailDTO>> nearByRouteOrRecommendationResult = new Gson().fromJson(resJson, new TypeToken<ResultJson<List<CommonFlyCityDetailDTO>>>() {
            }.getType());
            if (nearByRouteOrRecommendationResult != null) {
                nearByRouteOrRecommendation = nearByRouteOrRecommendationResult.getResultObject();
                isSetToRedis = true;
                redisResult = new Gson().toJson(nearByRouteOrRecommendationResult);
            }

        } else {
            ResultJson<List<CommonFlyCityDetailDTO>> resultJson = new Gson().fromJson(redisResult, new TypeToken<ResultJson<List<CommonFlyCityDetailDTO>>>() {
            }.getType());
            nearByRouteOrRecommendation = resultJson.getResultObject();
            isSetToRedis = false;
        }
        //
        //
        if (CollectionUtils.isNotEmpty(nearByRouteOrRecommendation)) {
            //默认存放一天 获取的是缓存数据不再存入redis
            if (isSetToRedis) {
                // API KEY
                redisUtil.set(key, redisResult, 60L * 60 * 24);
            }
            Map<String, CommonFlyCityBO> minMap = new HashMap<>();
            for (CommonFlyCityDetailDTO cfcDto : nearByRouteOrRecommendation) {
                //
                CommonFlyCityBO commonFlyCityBO = new CommonFlyCityBO();
                BeanUtils.copyProperties(cfcDto, commonFlyCityBO);
                //
                String cityCode = cfcDto.getCityCode();
                // 邻近航线跳过 出发城市 相同的
                // 热门推荐跳过 到达城市 相同的
                if ((isNearby && cfcRequest.getDepCode().equals(cityCode))
                        || (!isNearby && cfcRequest.getArrCode().equals(cityCode))) {
                    continue;
                }

                String minPrice = this.getMinPriceCache(isNearby, cfcRequest, cityCode, currency);

                // ？refactor
                if (!StringUtil.isNullOrEmpty(minPrice)) {
                    Double curMinPrice = Double.parseDouble(minPrice);
                    commonFlyCityBO.setMinPrice(curMinPrice);
                    if (minMap.isEmpty()) {
                        minMap.put("min", commonFlyCityBO);
                    } else {
                        if (curMinPrice < minMap.get("min").getMinPrice()) {
                            minMap.put("min", commonFlyCityBO);
                        }
                    }
                }
            }
            if (!minMap.isEmpty()) {
                ApiCityInfoDto arrCity = cacheService.getLocalCity(cfcRequest.getArrCode());
                ApiCityInfoDto depCity = cacheService.getLocalCity(cfcRequest.getDepCode());
                // 是否国际
                Boolean isInter = EnumInterNationTag.I.name().equals(depCity.getIsInternational()) || EnumInterNationTag.I.name().equals(arrCity.getIsInternational());

                CommonFlyCityBO min = minMap.get("min");
                //当查询航班没有时，展示配置的共飞城市中的最低价航线
                NearFlightRecommend flightRecommend = new NearFlightRecommend();
                BeanUtils.copyProperties(request, flightRecommend);
                if (isNearby) {
                    flightRecommend.setDepCityCode(min.getCityCode());
                    flightRecommend.setDepCityName(min.getCityName());
                    flightRecommend.setArrCityCode(cfcRequest.getArrCode());
                    flightRecommend.setArrCityName(arrCity.getCityName());
                } else {
                    flightRecommend.setDepCityCode(cfcRequest.getDepCode());
                    flightRecommend.setDepCityName(depCity.getCityName());
                    flightRecommend.setArrCityCode(min.getCityCode());
                    flightRecommend.setArrCityName(min.getCityName());
                }
                flightRecommend.setTripType(tripType);
                flightRecommend.setPrice(min.getMinPrice());
                flightRecommend.setRuleType(ruleType);
                return flightRecommend;
            }
        }
        return null;
    }


    /**
     * 获取低价缓存
     *
     * @param isNearby
     * @param cfcRequest
     * @param cityCode
     * @param currency
     * @return
     */
    private String getMinPriceCache(boolean isNearby, CFCModuleRequest cfcRequest, String cityCode, String currency) {
//         USE MOBILE KEY
//         MOBILE_KEY e.g.:API:test:common:flightMinPrice:MOBILE:MCNYOW2023-09-06CANSHA
//        String commonKey = "";
//        // 邻近航线，到达地相同
//        if (isNearby) {
//            commonKey = "M" + "CNY" + cfcRequest.getFlightType() + cfcRequest.getDepDate() + cityCode + cfcRequest.getArrCode();
//        } else {
//            // 热门推荐，出发地相同
//            commonKey = "M" + "CNY" + cfcRequest.getFlightType() + cfcRequest.getDepDate() + cfcRequest.getDepCode() + cityCode;
//        }
//        //
//        String minKey = redisConstantConfig.COMMON_FLIGHT_MIN_PRICE + "MOBILE" + ":" + commonKey;
//        if (TripTypeEnum.TRIP_TYPE_I.getCode().equals(tripType)) {
//            minKey = redisConstantConfig.COMMON_FLIGHT_TAXINFO + "MOBILE" + ":" + commonKey;
//        }
//        minKey = redisConstantConfig.getAPIRedisKey(minKey);
//        String minPrice = (String) redisUtil.get(minKey);

        // USE ONE ORDER MOBILE KEY
        // OW_KEY e.g.:OneOrder:LowPrice:B2C2023-09-05SHADLC
        // RT_KEY e.g.:OneOrder:LowPrice:B2C2023-09-062023-09-08SHAHELHELSHA
        String commonKey = "";
        // 邻近航线，到达地相同
        // 热门推荐，出发地相同
        if (isNearby) {
            if (RouteTypeEnum.RT.getCode().equals(cfcRequest.getFlightType())) {
                commonKey = cfcRequest.getDepDate() + cfcRequest.getArrCode() + cityCode + cfcRequest.getArrCode() + cfcRequest.getArrCode() + cityCode;
            } else {
                commonKey = cfcRequest.getDepDate() + cityCode + cfcRequest.getArrCode();
            }
        } else {
            if (RouteTypeEnum.RT.getCode().equals(cfcRequest.getFlightType())) {
                commonKey = cfcRequest.getDepDate() + cfcRequest.getArrCode() + cfcRequest.getDepCode() + cityCode + cityCode + cfcRequest.getDepCode();
            } else {
                commonKey = cfcRequest.getDepDate() + cfcRequest.getDepCode() + cityCode;
            }
        }
        String minKey = redisConstantConfig.REDIS0_ONEORDER_LOWPRICE_B2C + commonKey;
        // 若货币不为空，则拼接货币
        if (currency != null) {
            minKey += ":" + currency;
        }
        return (String) redis0Util.hget(minKey, "LowPrice");
    }

}
