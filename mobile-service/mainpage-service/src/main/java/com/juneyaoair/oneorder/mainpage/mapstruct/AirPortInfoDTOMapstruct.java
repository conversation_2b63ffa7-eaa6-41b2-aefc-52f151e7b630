package com.juneyaoair.oneorder.mainpage.mapstruct;

import com.juneyaoair.flightbasic.response.airport.AirPortInfoDTO;
import com.juneyaoair.oneorder.mainpage.dto.AirportInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/21 14:41
 */
@Mapper
public interface AirPortInfoDTOMapstruct {
    AirPortInfoDTOMapstruct mapper = Mappers.getMapper(AirPortInfoDTOMapstruct.class);

    List<AirportInfoDto> toAirportInfoDtoList(List<AirPortInfoDTO> airPortInfoDTOList);

    @Mappings({
            @Mapping(source = "cityInfo.cityName", target = "cityName"),
            @Mapping(source = "cityInfo.cityPinYin", target = "cityPinYin")
    })
    AirportInfoDto toAirportInfoDto(AirPortInfoDTO airPortInfoDTO);
}
