package com.juneyaoair.oneorder.mainpage.constant;

/**
 * @description 上传图片分类
 */
public enum PicTypeEnum {

    ABNOR_BAG("abnorbag","异常行李"),
    ID_PHOTO_IMAGE("IdPhotoImage","实名认证"),
    STUDENT_AUTH_IMAGE("studentAuthImage","学生认证"),
    MEMBER_HEAD_IMAGE("MemberHeadImage","会员头像"),
    LOST_ITEMS("lostItems","遗失物品"),
    SERVICE_BACK("serviceback","服务反馈"),
    SPECIAL_SERVICE("specialService","特殊服务"),
    CUSS_DELAY_PDF("cussPdf","CUSS航延证明"),
    REFUND_ORDER_IMAGE("refundOrderImage", "退票申请材料"),
    SPECIAL_REFUND_ORDER_IMAGE("specialRefundOrderImage", "特殊退票申请材料"),
    COMPENSATION("compensation", "电子补偿"),
    STUDENT("student","留学生运价"),
    MATERIAL("material","留学生证明材料"),
    EXCESS_BAGGAGE_RECEIPT("excessBaggageImage","逾重行李材料"),
    PETTRAVEL("petTravel","萌宠飞证明材料")


    ;

    private String code;
    private String desc;

    PicTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //检验类型
    public static PicTypeEnum check(String code){
        for (PicTypeEnum c: PicTypeEnum.values()) {
            if (c.code.equals(code)) {
                return c;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
