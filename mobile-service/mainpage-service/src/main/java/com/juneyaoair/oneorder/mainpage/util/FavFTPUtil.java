package com.juneyaoair.oneorder.mainpage.util;

import cn.hutool.extra.spring.SpringUtil;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpATTRS;
import com.jcraft.jsch.SftpException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.mainpage.config.EmailConfig;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;


public class FavFTPUtil {
    private static final Logger logger = LoggerFactory.getLogger(FavFTPUtil.class.getName());
    private static final String SEPARATOR = "/";
    //应用服务器上用于转移SFTP服务器文件的临时目录
    private static final String TEMP_FILE_PATH = "tempFile";
    // SFTP服务器上用于存放临时文件的路径
    private static final String SFTP_TEMP_FOLDER = "temp";

    /**
     * 创建远程目录
     *
     * @param sftpDirPath
     * @return 返回创建成功或者失败的代码和信息
     * @throws SftpException
     */
    public static String createDir(String sftpDirPath, SFTPUtil sftpTool) throws SftpException {
        ChannelSftp sftp = sftpTool.getSftp();
        cd("/", sftp);
        if (isDirExist(sftpDirPath, sftp)) {
            return "0:dir  is  exist  !";
        }
        String[] pathArry = sftpDirPath.split("/");
        for (String path : pathArry) {
            if (path.equals("")) {
                continue;
            }
            if (isDirExist(path, sftp)) {
                cd(path, sftp);
            } else {
                //建立目录
                sftp.mkdir(path);
                //进入并设置为当前目录
                sftp.cd(path);
            }
        }
        cd("/", sftp);
        return "1:创建目录成功";
    }

    /**
     * 判断文件是否存在
     *
     * @param directory
     * @param sftp
     * @return
     * @throws SftpException
     */
    public static boolean isDirExist(String directory, ChannelSftp sftp) throws SftpException {
        boolean isDirExistFlag = false;
        try {
            SftpATTRS sftpATTRS = sftp.lstat(directory);
            isDirExistFlag = true;
            return sftpATTRS.isDir();
        } catch (Exception e) {
            if (e.getMessage().equalsIgnoreCase("no such file")) {
                isDirExistFlag = false;
            }
        }
        return isDirExistFlag;
    }

    private static String createDirName() { //只获取年份和月份
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMM");
        //获取当前时间戳
        Date date = new Date();
        return simpleDateFormat.format(date);
    }

    /**
     * @param hostname    FTP服务器地址
     * @param port        FTP服务器端口号
     * @param username    FTP登录帐号
     * @param password    FTP登录密码
     * @param pathname    FTP服务器保存目录
     * @param fileName    上传到FTP服务器后的文件名称
     * @param inputStream 输入文件流
     * @return
     */
    public static boolean uploadFile(String hostname, int port, String username, String password, String pathname, String fileName, InputStream inputStream) {
        boolean flag = false;
        FTPClient ftpClient = new FTPClient();
        ftpClient.setControlEncoding("UTF-8");
        try {
            //连接FTP服务器
            ftpClient.connect(hostname, port);
            //登录FTP服务器
            ftpClient.login(username, password);
            //是否成功登录FTP服务器
            int replyCode = ftpClient.getReplyCode();
            if (!FTPReply.isPositiveCompletion(replyCode)) {
                return flag;
            }

            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            ftpClient.makeDirectory(pathname);
            ftpClient.changeWorkingDirectory(pathname);
            ftpClient.storeFile(fileName, inputStream);
            inputStream.close();
            ftpClient.logout();
            flag = true;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
        return flag;
    }

    private static boolean sftpUpload(InputStream is, String directory, String fileName) {
        SFTPUtil sftpUtil = new SFTPUtil();
        try {
            sftpUtil.connect(SpringUtil.getBean(EmailConfig.class).SFTP_HOSTNAME_01,
                    SpringUtil.getBean(EmailConfig.class).SFTP_PORT_01,
                    SpringUtil.getBean(EmailConfig.class).SFTP_USERNAME_01,
                    SpringUtil.getBean(EmailConfig.class).SFTP_PASSWORD_01);
            //查看目录是否存在，不存在则创建目录
            ChannelSftp sftp = sftpUtil.getSftp();
            createDir(directory, sftpUtil);
            sftp.cd(directory);
            sftp.put(is, fileName);
            return true;
        } catch (Exception e) {
            logger.error("通过文件流使用sftp上传到服务器失败!", e);
            return false;
        } finally {
            sftpUtil.closeChannel();
        }
    }

    public static boolean sftpUploadStream(InputStream is, String directory, String fileName,EmailConfig emailConfig) {
        SFTPUtil sftpUtil = new SFTPUtil();
        try {
            sftpUtil.connect(emailConfig.SFTP_HOSTNAME_01,
                    emailConfig.SFTP_PORT_01,
                    emailConfig.SFTP_USERNAME_01,
                    emailConfig.SFTP_PASSWORD_01);
            return  sftpUtil.upload(is,directory,fileName);
        } catch (Exception e) {
            logger.error("通过文件流使用sftp上传到服务器失败!", e);
            return false;
        } finally {
            sftpUtil.closeChannel();
        }
    }


    public static boolean sftpUpload(String filePath,String directory,String fileName,EmailConfig emailConfig) {
        SFTPUtil sftpUtil = new SFTPUtil();
        try {
            sftpUtil.connect(emailConfig.SFTP_HOSTNAME_01,
                    emailConfig.SFTP_PORT_01,
                    emailConfig.SFTP_USERNAME_01,
                    emailConfig.SFTP_PASSWORD_01);
            return sftpUtil.upload(filePath,directory,fileName);
        } catch (Exception e) {
            logger.error("通过文件流使用sftp上传到服务器失败!", e);
            throw new ServiceException(e.getMessage());
        } finally {
            sftpUtil.closeChannel();
        }
    }



    /**
     * @param originFile 原始文件
     * @param targetPath 目标目录
     * @param fileName   文件名称
     */
    public static void copyRemoteFile(String originFile, String targetPath, String fileName) {
        SFTPUtil sftpUtil = new SFTPUtil();
        try {
            sftpUtil.connect(SpringUtil.getBean(EmailConfig.class).SFTP_HOSTNAME_01, SpringUtil.getBean(EmailConfig.class).SFTP_PORT_01,
                    SpringUtil.getBean(EmailConfig.class).SFTP_USERNAME_01, SpringUtil.getBean(EmailConfig.class).SFTP_PASSWORD_01);
            if (!sftpUtil.isFileExist(originFile)) {
                throw new ServiceException("目标文件名不存在！");
            }
            //查看目录是否存在，不存在则创建目录
            ChannelSftp sftp = sftpUtil.getSftp();
            createDir(targetPath, sftpUtil);
            sftp.cd(targetPath);
            sftp.rename(originFile, targetPath + SEPARATOR + fileName);
        } catch (JSchException | SftpException e) {
            throw new ServiceException("移动文件失败！:" + e.getMessage());
        } finally {
            sftpUtil.closeChannel();
        }
    }


    /**
     * 进入指定的目录并设置为当前目录
     *
     * @param sftpPath
     * @throws Exception
     */
    public static void cd(String sftpPath, ChannelSftp sftp) throws SftpException {
        sftp.cd(sftpPath);
    }
}
