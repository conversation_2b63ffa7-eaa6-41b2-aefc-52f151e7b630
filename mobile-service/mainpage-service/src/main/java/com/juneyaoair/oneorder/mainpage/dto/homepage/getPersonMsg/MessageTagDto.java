package com.juneyaoair.oneorder.mainpage.dto.homepage.getPersonMsg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 消息标签DTO
 */
@ApiModel(description = "消息标签DTO")
@Data
public class MessageTagDto {

    /**
     * 消息标签ID
     */
    @ApiModelProperty("消息标签ID")
    private String dataId;

    /**
     * 消息标签名称
     */
    @ApiModelProperty("消息标签名称")
    private String messageTagName;

    /**
     * 消息标签颜色
     */
    @ApiModelProperty("消息标签颜色")
    private String messageTagColor;
    /**
     * 消息标签背景颜色
     */
    @ApiModelProperty("消息标签背景颜色")
    private String messageTagBackgroundColor;

}
