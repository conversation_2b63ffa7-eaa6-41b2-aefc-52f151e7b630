package com.juneyaoair.oneorder.mainpage.dto.tripcert;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@ApiModel
@Data
public class DeliveryBooking{
    @ApiModelProperty(value = "渠道订单号渠道系统中的唯一订单号，不能重复", required = true)
    @NotNull(message="渠道订单号不能为空")
    private String channelOrderNo; //渠道订单号渠道系统中的唯一订单号，不能重复
    @ApiModelProperty("乘机人信息")
    @NotNull(message = "乘机人信息不能为空")
    @Size(min = 1,max = 6,message = "至少包含一位乘客信息，最多六位乘客")
    @Valid
    private List<DeliveryTicketInfo> deliveryTicketInfoList;
    /**
     * 邮寄地址
     */
    @ApiModelProperty(value = "邮寄地址", required = true)
    @NotNull(message="地址信息不能为空")
    @Valid
    private DeliveryAddress deliveryAddress;
    @ApiModelProperty("/ 邮寄方式")
    private String deliveryType;/// 邮寄方式
    @ApiModelProperty("/ 邮寄费用")
    private Double deliveryFee;/// 邮寄费用

}
