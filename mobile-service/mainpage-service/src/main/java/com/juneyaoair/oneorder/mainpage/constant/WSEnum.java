package com.juneyaoair.oneorder.mainpage.constant;

/**
 * Created by qinxia<PERSON><PERSON> on 2016-4-15.
 */
public enum WSEnum {
    /**
     * 10001表示成功 1000X-表示业务类提示信息
     */
    SUCCESS("10001", "正常！"),
    NO_DATA("10002", "查询无数据！"),
    ERROR_REQUEST_PARAMS("10003", "输入参数有问题！"),
    ERROR_REQUEST_PARAMS_925_1("10003", "请确认输入的信息是否正确"),
    ERROR_REQUEST_PARAMS_925_2("10003", "请确认输入的信息是否完整"),
    ERROR_REQUEST_PARAMS_925_3("10003", "登录信息不完整，请重新登陆后再试"),
    ERROR_REQUEST_PARAMS_925_4("10003", "请输入正确格式的信息"),

    ERROR_REQUEST_PARAMS_925("10003", "请输入正确格式的信息！"),
    ERROR_QUERY_ERROR("10004", "查询运价出错！"),
    ERROR("10005", "系统繁忙！"),
    ERROR_925("10005", "小吉开小差啦，请稍后再试"),
    ERROR_CHECK_NULL("10006", "值机错误！"),
    ERROR_CON_CHECK_NULL("10007", "取消值机错误！"),
    REPEAT_UBMISSION("10008", "重复提交！"),
    NO_DEVICE_BLACKBOX("10009", "未获取到设备指纹！"),
    ERROR_USER_INFO("10010", "其它登录方式查询信息错误！"),
    ERROR_TRANSFER("10011", "调用接口返回不符合要求的结果！"),
    HAVE_UNPAY_SEAT("10098", "该航班尚有未支付的付费座位，请先支付"),
    CHECKIN_CANCEL_COUNT_LIMIT("10099", "您已取消过2次，请到值机柜台办理取消值机"),
    AIRPORT_BOARDING_PASS_NOT_SUPPORT("10100", "该机场暂时不支持电子安检验讫"),
    REFUND_PAY_SEAT_TIME_LIMIT("10101", "距航班起飞已少于48小时，已不能申请自愿退款，如有疑问请联系客服"),
    MEMBER_LEVEL_INSUFFICIENT("10102", "抱歉，此功能目前只针对金卡/白金卡用户开放，您尚未达标，请继续努力！"),
    APPLY_MEMBER_LEVEL_INSUFFICIENT("10103", "抱歉，实体卡申请目前只针对银卡/金卡/白金卡用户开放，外部渠道匹配金卡挑战资格、体验金卡等不适用"),
    NETWORK_ERROR("10104", "网络繁忙，请稍后再试！"),
    SERVICE_BUSY_ERROR("10105", "服务繁忙，请稍后再试！"),
    INSURANCE_COMBINED_FLIGHT("100011", "联程航班暂不支持单独购保！"),
    INSURANCE_BABY_FLIGHT("100010", "婴儿票暂不支持单独购保！"),
    INSURANCE_TICKET_ERROR("100010", "很抱歉，您输入的票号暂无可购买的保险"),
    CANNOT_USE_SCORE("100012", "不可使用积分"),
    CANNOT_QUERY_USE_TICKET("100013", "未查询到符合要求的客票，请重新查询"),
    //弹框错误提示类
    ALERT_ERROR_MESSAGE("21000", "错误！"),
    ALERT_ERROR_ORDER_MESSAGE("21000", "此产品卡存在关联订单，先进行退票或者取消兑票订单后再申请退卡"),
    ALERT_ERROR_NOSHOW_MESSAGE("21000", "此产品卡或关联卡已达到NOSHOW上限，已无法申请退卡"),
    ERROR_LONGIN_CHK_ERROR("21001", "登录检验出错！"),
    ERROR_LONGIN_ERROR("21002", "登录出错！"),
    ERROR_CHK_ERROR("21003", "检验出错！"),
    ERROR_CHK_ERROR_925("21003", "抱歉，登录信息不完整，请重新登陆后再试"),
    ERROR_CHK_ERROR_925_1("21003", "抱歉，请确认输入的信息是否完整"),
    ERROR_CHK_ERROR_925_2("21003", "小吉开小差啦，请稍后再试"),
    TOAST_ERROR("21004", "订单处理中，请稍后再尝试"),
    LOGOUT_VERIFY_ERROR("21005", "您的账号存在未完成的订单"),
    RECEIVE_FAIL("21006", "领取失败"),
    BIND_VERIFY_FAIL("21007", "非常抱歉，您的实名认证方式不符合产品规则，请重新实名认证后再尝试操作"),
    BIND_VERIFY_ERROR("21008", "非常抱歉，您的中、英文姓名不一致，请联系95520修改实名认证信息后再尝试绑定本人操作"),
    QUERY_BIND_ERROR("21009", "此产品卡已退款成功"),
    UPCLASSCARD_NOT_PURCHASED("21010", "升舱卡未购买"),
    UPCLASSCARD_UNBIND("21011", "升舱卡未绑定，请先进行绑定。"),
    UPCLASSCARD_CHECKED_IN("21012", "客票已经值机，请先取消值机再办理升舱"),
    ONLY_IDCARD_SPEEDREFUND("21013", "非身份证客票暂不支持极速退款，请联系原订票渠道办理退票"),
    CHECK_IN_FAILED("21014", "旅客【%s】值机操作失败，请重新办理"),

    WX_FFP_USER_NOT_BINDING("30002", "会员未绑定微信用户"),
    WX_NOT_TEMPLATE_MESSAGE("30003", "模版消息不存在，或者已经失效"),
    CHOOSE_HINT("30004","请选择后续操作"),

    //5开头代表页面需要根据代码跳转，系统类错误
    USER_NO_BIND("50001", "会员未绑定第三方账号"),
    INVALID_TOKEN("50002", "登录凭证已失效，请重新登录"),
    NO_REAL_NAME("50003", "您还未实名，请先完成实名认证！"),
    REPEATED_PASS("50004", "常用乘机人信息重复！"),
    CERT_NUMBER_EXIST("50005", "证件号已存在"),
    OPERATION_TIMEOUT("50006", "操作超时，请重新认证"),
    DUPLICATE_REQUEST("50007", "重复的请求"),
    BLACK_USER_REGISTER("50008", "该手机号注册失败，如有疑问可拨打客服电话95520进行咨询"),
    COMPANY_INFO_ERROR("50009", "抱歉，您所在的企业目前并未与吉祥航空建立大客户合作关系，暂时无法享受企业认证会员专属权益，如需建立合作，请致电021-********商谈"),
    PAYMENT_OVER_TIME("50010", "支付超时!支付链接已失效!"),
    ACCOUNT_LOGIN_ON_OTHER_MOBILE("50011", "该账号在其他手机登录"),
    MOBILE_NO_TOKEN("50013", "请登录后访问"),
    NETWORK_BUSY("50014", "网络繁忙，请稍后再试！"),
    // 错误并展示引导按钮
    SHOW_PILOT_BUTTON("50015", "错误！"),

    ACTIVITY_COMPLETED("60001", "活动已过期！"),
    ACTIVITY_UNSTARTED("60002", "活动未开始"),
    ACTIVITY_LNELIGIBLE("60003", "您还未获得参加活动的资格"),
    ACTIVITY_ATTENDED("60004", "您已参与过该活动"),
    //7风险类提示
    RISK_REJECT("70000", "风险系数过高"),
    TONGDUN_FAIL_LOGIN("70001", "抱歉，该账号风险系数高，无法登录"),
    TONGDUN_FAIL_REGISTER("70002", "抱歉，该账号风险系数高，无法注册"),
    TONGDUN_FAIL_TRADE("70003", "抱歉，该账号风险系数高，无法提交订单"),
    TONGDUN_FAIL_RECEIVE("70004", "抱歉，领取失败！"),
    TONGDUN_CHECKIN_FAIL_TRADE("70005", "抱歉，该账号风险系数高，无法查询"),

    PWD_CHK_ERROR("99999", "密码错误！"),

    //大数据响应结果枚举
    SUC000000("000000", "请求成功");
    private String resultCode;
    private String resultInfo;

    WSEnum(String resultCode, String resultInfo) {
        this.resultCode = resultCode;
        this.resultInfo = resultInfo;
    }

    @Override
    public String toString() {
        return super.toString() + "(" + resultCode + "," + resultInfo + ")";
    }

    public String getResultInfo() {
        return resultInfo;
    }

    public String getResultCode() {
        return resultCode;
    }
}