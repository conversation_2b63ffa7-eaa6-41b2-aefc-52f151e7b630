package com.juneyaoair.oneorder.mainpage.service.impl;

import com.juneyaoair.flightbasic.commondto.RequestData;
import com.juneyaoair.flightbasic.commondto.ResponseData;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.request.airLine.AttentionFlightParam;
import com.juneyaoair.flightbasic.request.airLine.CancelAttentionFlightParam;
import com.juneyaoair.flightbasic.response.airline.FollowAirLineResDTO;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.dsop.dto.FlightInfoDynamicDto;
import com.juneyaoair.oneorder.api.dsop.dto.PtFlightStatusReq;
import com.juneyaoair.oneorder.api.dsop.service.IDsopService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.mainpage.service.HoCarPushService;
import com.juneyaoair.oneorder.mainpage.service.IAttentionFlightService;
import com.juneyaoair.oneorder.mainpage.service.IFlightService;
import com.juneyaoair.oneorder.restresult.enums.SuccessCode;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/9 17:33
 */
@Service
public class AttentionFlightService implements IAttentionFlightService {
    @Autowired
    private IBasicService basicService;
    @Autowired
    private IDsopService dsopService;
    @Autowired
    private IFlightService flightService;
    @Autowired
    private FlightBasicProviderClient flightBasicProviderClient;
    @Autowired
    private HoCarPushService hoCarPushService;

    @Override
    public List<FlightInfoDynamicDto> queryAttentionFlightList(String ffpId, String ffpCardNo, BizDto bizDto) {
        List<FollowAirLineResDTO> followAirLineResDTOList = basicService.queryAttentionFlightList(bizDto, ffpCardNo);
        if (CollectionUtils.isNotEmpty(followAirLineResDTOList)) {
            List<FlightInfoDynamicDto> newFlightInfoDynamicDtoList = followAirLineResDTOList.parallelStream().map(followAirLineResDTO -> {
                PtFlightStatusReq ptFlightStatusReq = PtFlightStatusReq.builder()
                        .flightDateLocal(followAirLineResDTO.getFlightDate())
                        .flightNo(followAirLineResDTO.getFlightNo())
                        .departureAirport(followAirLineResDTO.getDepAirportCode())
                        .arrivalAirport(followAirLineResDTO.getArrAirportCode()).build();
                List<FlightInfoDynamicDto> flightInfoDynamicDtoList = dsopService.searchFlightDynamicsInfo(ptFlightStatusReq, null);
                if (CollectionUtils.isNotEmpty(flightInfoDynamicDtoList)) {
                    FlightInfoDynamicDto flightInfoDynamicDto = flightInfoDynamicDtoList.get(0);
                    flightInfoDynamicDto.setConcert(true);
                    flightInfoDynamicDto.setConcertId(followAirLineResDTO.getId());
                    //完善动态信息
                    flightService.supplyFlightInfoDynamicDto(bizDto, flightInfoDynamicDto);
                    return flightInfoDynamicDto;
                }
                return null;
            }).collect(Collectors.toList());
            return newFlightInfoDynamicDtoList.stream().sorted(
                            Comparator.comparing(FlightInfoDynamicDto::getStd))
                    .collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public ResponseData addAttentionFlight(RequestData<AttentionFlightParam> requestData) {
        ResponseData responseData = flightBasicProviderClient.addAttentionFlight(requestData);
        //HOCAR渠道，异步同步日程助手
        if (SuccessCode.SUCCESS.getCode().equals(responseData.getCode()) && ChannelCodeEnum.HOCAR.getChannelCode().equals(requestData.getChannelNo())) {
            hoCarPushService.pushFlight(requestData, 0, 0);
        }
        return responseData;
    }

    @Override
    public ResponseData cancelAttentionFlight(RequestData<CancelAttentionFlightParam> requestData) {
        ResponseData responseData = flightBasicProviderClient.cancelAttentionFlight(requestData);
        //大出行渠道，异步取消日程助手
        if (SuccessCode.SUCCESS.getCode().equals(responseData.getCode()) && ChannelCodeEnum.HOCAR.getChannelCode().equals(requestData.getChannelNo())) {
            hoCarPushService.cancelPushFlight(requestData, 2, 0);
        }
        return responseData;
    }
}
