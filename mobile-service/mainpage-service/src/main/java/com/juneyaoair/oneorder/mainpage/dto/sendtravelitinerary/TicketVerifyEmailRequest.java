package com.juneyaoair.oneorder.mainpage.dto.sendtravelitinerary;

import com.juneyaoair.oneorder.constant.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@ApiModel(description = "")
@Data
public class TicketVerifyEmailRequest {

    @ApiModelProperty("票号")
    @NotBlank(message="票号不能为空")
    private String ticketNo;
    @ApiModelProperty("旅客姓名")
    @NotBlank(message="旅客姓名不能为空")
    private String passengerName;
    @ApiModelProperty("邮箱")
    @Pattern(regexp = PatternCommon.EMAIL, message = "邮箱格式不正确")
    private String email;
    /**
     * 0：中文  1：英文
     */
    @ApiModelProperty(value = "0：中文  1：英文  2:根据语言选择", required = true)
    @NotBlank(message="邮件类型不能为空")
    private String type;

    /**
     * 手机号码 中英文版不能为空
     */
    @ApiModelProperty("手机号码 中英文版不能为空")
    private String mobileNo;
    /**
     * 短信验证码 中英文版不能为空
     */
    @ApiModelProperty("短信验证码 中英文版不能为空")
    private String veriCode;
    @ApiModelProperty(value = "是否下载",example = "1:下载；其他：预览",hidden = true)
    public String downloadFlag;
    @ApiModelProperty("指纹设备")
    public String blackBox;
}
