package com.juneyaoair.oneorder.mainpage.dto.suggestion;

import com.juneyaoair.oneorder.mainpage.constant.MainPageConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @Description 意见反馈请求参数
 **/
@ApiModel(description = "意见反馈请求参数*/")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ExpressViewsRequest {

    /**
     * 航班号
     */
    @ApiModelProperty("航班号")
    private String flightNo;

    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    private String flightDate;

    /**
     * 类型
     * 1:表扬,2:投诉,3:意见建议
     */
    @ApiModelProperty(value = "类型 1:表扬,2:投诉,3:意见建议", required = true)
    @NotNull(message = "意见类型不能为空")
    @Pattern(regexp = "[1,2,3]", message = "类型不正确")
    private String type;

    /**
     * 旅客姓名
     */
    @ApiModelProperty(value = "旅客姓名", required = true)
//    @Pattern(regexp = MainPageConstant.CN_EN_REG_EXP +"{1,10}", message = "旅客姓名只支持中英文，且最多输入10个字符")
    @NotNull(message = "旅客姓名不能为空")
    private String passName;

    /**
     * 旅客证件号
     */
    @ApiModelProperty(value = "旅客证件号", required = true)
//    @NotNull(message = "旅客证件号不能为空")
    private String passIdCard;

    /**
     * 旅客联系方式
     */
    @ApiModelProperty(value = "旅客联系方式", required = true)
    @NotNull(message = "旅客联系方式不能为空")
//    @Pattern(regexp = "[0-9-_,]+", message = "联系方式仅支持数字及标点")
    private String passContact;

    /**
     * 内容
     */
    @ApiModelProperty(value = "内容", required = true)
    @NotNull(message = "意见内容不能为空")
    private String content;

    /**
     * 附件地址
     * 多个附件地址用逗号隔开
     */
    @ApiModelProperty("附件地址 多个附件地址用逗号隔开")
    private String url;
}
