package com.juneyaoair.oneorder.mainpage.controller;

import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.passager.IPassagerService;
import com.juneyaoair.oneorder.api.passager.dto.*;
import com.juneyaoair.oneorder.common.util.BeanUtils;
import com.juneyaoair.oneorder.mainpage.constant.WSEnum;
import com.juneyaoair.oneorder.mainpage.dto.lostitems.LostItemsFeedbackReq;
import com.juneyaoair.oneorder.mainpage.dto.lostitems.LostItemsFeedbackRequest;
import com.juneyaoair.oneorder.mainpage.dto.lostitems.LostItemsQueryRequest;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Api(value = "LostItemsController", tags = "遗失物品")
@RestController
@RequestMapping("lostItems")
@Slf4j
public class LostItemsController {

    @Resource
    private IPassagerService passagerService;

    /**
     * 机上遗失物品查询（遗失物品登记查询）
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "遗失物品登记查询", notes = "机上遗失物品查询（遗失物品登记查询）")
    @RequestMapping(value = "getLostItems", method = RequestMethod.POST)
    public ResponseData<List<LostItems>> getLostItems(@RequestBody RequestData<LostItemsQueryRequest> request, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }

        request.getData().setChannelCode(request.getChannelNo());
        request.getData().setFfpId(request.getFfpId());

        ResponseData<List<LostItems>> baseResponse = ResponseData.success();
        LostItemsRequest lireq = new LostItemsRequest();
        request.getData().setLbTypeNew(request.getData().getLbType());
        lireq.setData(request.getData());
        lireq.setInftCode("remainQuery");
        lireq.setMemberId(request.getFfpId());
        LostItemsQueryResponse ltr = passagerService.lostAndFoundQuery(lireq);
        if (ltr == null || !"0".equals(ltr.getCode())) {
            throw new ServiceException("查询失败");
        }
        baseResponse.setData(Optional.ofNullable(ltr)
                .map(LostItemsQueryResponse::getData)
                .map(LostItemsRsp::getDataList).orElse(null));
        return baseResponse;
    }

    /**
     * 机上遗失物品报失（旅客遗留物品登记）
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "旅客遗留物品登记", notes = "机上遗失物品报失（旅客遗留物品登记）")
    @RequestMapping(value = "lostItems_feedBack", method = RequestMethod.POST)
    public ResponseData<String> lostItemsFeedBack(@RequestBody RequestData<LostItemsFeedbackRequest> req, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            throw new ArgumentCheckFailException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        ResponseData<String> res = ResponseData.success();
        List<String> files = req.getData().getFileUrls();
        StringBuffer filePath = new StringBuffer();
        if (null != files && files.size() > 0) {
            for (int i = 0; i < files.size(); i++) {

                if (i == files.size() - 1) {
                    filePath.append(files.get(i));
                } else {
                    filePath.append(files.get(i) + ",");
                }
            }
            req.getData().setFilePath(filePath.toString());
        }
        LostItemsRequest lireq = new LostItemsRequest();
        lireq.setMemberId(req.getFfpId());
        lireq.setInftCode("remainAdded");
        LostItemsFeedbackReq lostItemsFeedbackReq = new LostItemsFeedbackReq();
        BeanUtils.copyNotNullProperties(req.getData(), lostItemsFeedbackReq);
        lostItemsFeedbackReq.setLeaveBehindLevelNew(req.getData().getLeaveBehindLevel());
        lireq.setData(lostItemsFeedbackReq);

        LostItemsResponse ltr = passagerService.checkInitemsLost(lireq);
        if (ltr==null) {
            throw new ServiceException("遗失物品报失失败！");
        }
        if ("0".equals(ltr.getCode())) {
            res.setData("遗失物品报失成功，我们将在3个工作日内联系您，您可在我的报失页面查看进展！");
        } else if (ltr.getMsg().contains("已有")) {
            log.info("接口返回错误信息为：{}", ltr.getMsg());
            res.setData("您已提交成功，请勿重复提交！");
        } else {
            log.info("接口返回错误信息为：{}", ltr.getMsg());
            res.setCode(WSEnum.ERROR.getResultCode());
            res.setData(ltr.getMsg());
        }
        return res;
    }

}
