package com.juneyaoair.oneorder.mainpage.util;


import com.lowagie.text.DocumentException;
import com.lowagie.text.pdf.BaseFont;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.*;


@Slf4j
public class PdfUtil {
    //字体路径
    public static final String PDF_FONT_PATH =  "fonts/simsun.ttc";

    private static String loadedFontPath() throws IOException {
        // 配置中文字体（统一使用类路径加载）
        ClassPathResource fontResource = new ClassPathResource(PDF_FONT_PATH,Thread.currentThread().getContextClassLoader());
        String path = fontResource.getURL().toString();
        log.info("当前字体路径:{}",path);
        return path;
    }

    public static void createPdf(String htmlStr, String filePath) throws IOException, DocumentException {
        ITextRenderer renderer = new ITextRenderer();
        ITextFontResolver fontResolver = renderer.getFontResolver();
        String path =loadedFontPath();
        fontResolver.addFont(path, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        renderer.setDocumentFromString(htmlStr);
        renderer.layout();
        try (OutputStream os = new FileOutputStream(filePath)) {
            renderer.createPDF(os);
            renderer.finishPDF();
        }
    }

    public static ByteArrayInputStream exportPdf(String template) throws Exception {
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            ITextRenderer renderer = new ITextRenderer();
            ITextFontResolver fontResolver = renderer.getFontResolver();
            // 配置中文字体（统一使用类路径加载）
            String fontPath =loadedFontPath();
            fontResolver.addFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
            renderer.setDocumentFromString(template);
            renderer.layout();
            renderer.createPDF(byteArrayOutputStream, false);
            renderer.finishPDF();
            byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
        } catch (DocumentException e) {
            log.info(e.getMessage(), e);
        }

        return byteArrayInputStream;
    }

}
