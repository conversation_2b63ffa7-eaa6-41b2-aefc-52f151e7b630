package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.oneorder.api.email.dto.SendEmailCodeParam;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.mainpage.dto.travelitinerary.PdfTravelItineraryUrlRequest;
import com.juneyaoair.oneorder.mainpage.dto.sendtravelitinerary.TicketVerifyEmailRequest;
import com.juneyaoair.oneorder.mainpage.dto.verify.FlightVerifyReq;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.ticket.FlightItineraryInfo;
import org.springframework.validation.BindingResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface IItineraryService {
    /**
     * 行程确认单
     */
    ResponseData<FlightItineraryInfo> ticketVerify(RequestDataDto<FlightVerifyReq> paramDetrInfo, BindingResult bindingResult, BizDto bizDto);

    /**
     * 生成行程单
     */
    ResponseData<String> sendTravelItinerary(RequestDataDto<TicketVerifyEmailRequest> req,BizDto bizDto, HttpServletRequest httpReq);

    /**
     * 客票验真pdf预览
     **/
    ResponseData<String> pdfTravelItineraryUrl(RequestDataDto<PdfTravelItineraryUrlRequest> req,BizDto bizDto, HttpServletResponse response);

    /**
     * 邮箱验证码
     **/
    void sendEmailCode(BizDto bizDto,String ffpNo, SendEmailCodeParam sendEmailCodeParam, LanguageEnum language);
}
