package com.juneyaoair.oneorder.mainpage.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EmailConfig {
    @Value("${sftp_password_01:XtJrXjVu}")
    public String SFTP_PASSWORD_01;
    @Value("${sftp_username_01:juneyaoair}")
    public String SFTP_USERNAME_01;
    @Value("${sftp_port_01:22}")
    public  int SFTP_PORT_01;
    @Value("${sftp_img_path_01:/upload/}")
    public String SFTP_IMG_PATH_01;
    @Value("${sftp_tomcate_port_01:https://mediaws.juneyaoair.com}")
    public String SFTP_TOMCAT_PORT_01;
    @Value("${sftp_address_01:mediaws.juneyaoair.com}")
    public String SFTP_HOSTNAME_01;
    @Value("${sftp_pathname_01:/juneyaoair/upload}")
    public String SFTP_PATHNAME_01;


    @Value("${sftp_itineraimg_path_01:/upload/b2c/itinerary}")
    public String SFTP_itinerary_IMG_PATH_01;
    @Value("${sftp_itinerary_path:/juneyaoair/upload/b2c/itinerary}")
    public String SFTP_itinerary_path;
}
