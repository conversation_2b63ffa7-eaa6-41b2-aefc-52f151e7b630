package com.juneyaoair.oneorder.mainpage.dto.electronic;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 电子行程单冲红参数
 * @created 2025/4/21 17:24
 */
@Data
public class ElectronicCancelPostParam {

    @NotBlank(message = "签名信息不能为空")
    @ApiModelProperty(value = "签名", required = true)
    private String signature;

    @NotBlank(message = "票号不能为空")
    @ApiModelProperty(value = "票号", required = true)
    private String ticketNumber;

    @NotBlank(message = "发票号码不能为空")
    @ApiModelProperty(value = "发票号码", required = true)
    private String invoiceNumber;

}
