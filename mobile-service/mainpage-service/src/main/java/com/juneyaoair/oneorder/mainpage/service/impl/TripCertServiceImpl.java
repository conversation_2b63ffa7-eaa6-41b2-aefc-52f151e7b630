package com.juneyaoair.oneorder.mainpage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.reflect.TypeToken;
import com.juneyaoair.flightbasic.common.UnifiedOrderResultEnum;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.constant.VoucherTypesEnum;
import com.juneyaoair.oneorder.api.order.dto.ticket.*;
import com.juneyaoair.oneorder.api.order.service.IOrderService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.request.LimitCount;
import com.juneyaoair.oneorder.mainpage.config.MainPageConfig;
import com.juneyaoair.oneorder.mainpage.config.TripCertConfig;
import com.juneyaoair.oneorder.mainpage.constant.WSEnum;
import com.juneyaoair.oneorder.mainpage.dto.tripcert.*;
import com.juneyaoair.oneorder.mainpage.service.ITripCertService;
import com.juneyaoair.oneorder.util.FlightUtil;
import com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.CouponProductBuyRequestDto;
import com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.CouponProductBuyResponseDto;
import com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.MailTravelProduct;
import com.juneyaoair.oneorder.order.feign.BenefitOrderClient;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.order.constant.TicketStateEnum;
import com.juneyaoair.oneorder.order.dto.TicketInfoRequest;
import com.juneyaoair.oneorder.constant.PatternCommon;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.*;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class TripCertServiceImpl implements ITripCertService {
    @Resource
    IOrderService orderService;
    @Resource
    MainPageConfig mainPageConfig;
    @Resource
    CommonService commonService;
    @Resource
    CacheService cacheService;
    @Resource
    TripCertConfig tripCertConfig;
    @Resource
    BenefitOrderClient benefitOrderClient;


    @Override
    public ResponseData<SubmitTripOrderResp> submitTripOrder(RequestData<DeliveryBooking> baseReq, HttpServletRequest request, BindingResult bindingResult) {
        ResponseData<SubmitTripOrderResp> resp = ResponseData.success();
        log.info("【邮寄行程单提交】IP地址:{}，客户端提交参数：{}", ServiceContext.getHead().clientIp, JSONUtil.toJsonStr(baseReq));
        DeliveryBooking deliveryBooking = baseReq.getData();
        if (deliveryBooking == null) {
            throw new ServiceException("请求参数不完整");
        }
        //验证必要的请求参数
        if (bindingResult.hasErrors()) {
            throw new ServiceException(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        //验证票号和姓名
        for (DeliveryTicketInfo deliveryTicketInfo : deliveryBooking.getDeliveryTicketInfoList()) {
            if (!deliveryTicketInfo.getCheckFlag().equals(EncoderHandler.encodeByMD5(deliveryTicketInfo.getTicketNo()
                    + deliveryTicketInfo.getPassName()))) {
                throw new ServiceException("客票与姓名不匹配！");
            }
        }
        TripCert tripCert = JSONUtil.toBean(tripCertConfig.tripCert, new TypeToken<TripCert>() {
        }.getType(), true);
        if (deliveryBooking.getDeliveryFee() == null || tripCert.getShipPrice() != deliveryBooking.getDeliveryFee().doubleValue()) {
            throw new ServiceException("价格金额有误！");
        }
        //提交订单
        RequestData<CouponProductBuyRequestDto> requestData = getRequestData(baseReq, deliveryBooking);
        ResponseData<CouponProductBuyResponseDto> responseData = benefitOrderClient.buyCouponProduct(requestData);

        if (responseData == null || responseData.getData() == null
                || !UnifiedOrderResultEnum.SUCCESS.name().equalsIgnoreCase(responseData.getCode())) {
            throw ServiceException.fail(Optional.ofNullable(responseData).map(ResponseData::getMessage).orElse("系统繁忙"));
        }
        CouponProductBuyResponseDto couponProductBuyResponse = responseData.getData();
        resp.setMessage(WSEnum.SUCCESS.getResultInfo());
        SubmitTripOrderResp orderInfo = new SubmitTripOrderResp();
        orderInfo.orderNo = couponProductBuyResponse.getOrderNo();
        orderInfo.channelOrderNo = couponProductBuyResponse.getChannelOrderNo();
        resp.setData(orderInfo);
        return resp;
    }

    @NotNull
    private RequestData<CouponProductBuyRequestDto> getRequestData(RequestData<DeliveryBooking> baseReq, DeliveryBooking deliveryBooking) {
        ChannelInfo channelInfo = ServiceContext.getHead().channelInfo;
        //转换请求参数
        RequestData<CouponProductBuyRequestDto> requestData = new RequestData<>();
        CouponProductBuyRequestDto couponProductBuyRequest = createCouponProBuyReq(deliveryBooking, channelInfo.getChannelCode(), channelInfo.getUserNo());
        couponProductBuyRequest.setOrderRequestIp(ServiceContext.getHead().clientIp);
        requestData.setData(couponProductBuyRequest);
        requestData.setChannelNo(baseReq.getChannelNo());
        requestData.setFfpNo(baseReq.getFfpNo());
        requestData.setFfpId(baseReq.getFfpId());
        requestData.setOriginIp(baseReq.getOriginIp());
        return requestData;
    }


    @Override
    public ResponseData<List<DeliveryTicketInfo>> checkTicket(RequestData<DeliveryTicketInfoUser> baseReq, BindingResult bindingResult, HttpServletRequest request) {
        ResponseData<List<DeliveryTicketInfo>> resp = ResponseData.success();
        DeliveryTicketInfoUser deliveryTicketInfoUser = baseReq.getData();
        if (deliveryTicketInfoUser == null) {
            throw ServiceException.fail("请求参数不完整");
        }
        //验证必要的请求参数
        if (bindingResult.hasErrors()) {
            resp.setCode(WSEnum.ERROR_REQUEST_PARAMS.getResultCode());
            resp.setMessage(bindingResult.getAllErrors().get(0).getDefaultMessage());
            return resp;
        }
        ChannelInfo channelInfo = ServiceContext.getHead().channelInfo;
        String clientIp = ServiceContext.getHead().clientIp;
        String channelCode = channelInfo.getChannelCode();
        String userNo = channelInfo.getUserNo();
        String ffpCardNo = ServiceContext.getHead().ffpNo;
        //记录手机IP对应关系
        Map<String, Object> info = new HashMap<>();
        info.put("ffpcardNo", ffpCardNo);
        info.put("ip", clientIp);
        commonService.saveDayInfo("TRIP_TICKET", clientIp, ffpCardNo, info, DateUtil.addOrLessDay(new Date(), 1));
        //客票操作次数检验
        LimitCount limitCount = mainPageConfig.getTripTicketIpLimit();
        boolean chkFlag = commonService.chkCountOperation(clientIp, "TRIP_TICKET", "", limitCount, resp);
        if (!chkFlag) {
            commonService.countOperation(clientIp, "TRIP_TICKET", "", limitCount);
            return resp;
        }
        String passName = deliveryTicketInfoUser.getPassName().toUpperCase();
        Map<String, String> headMap = HttpUtil.getHeaderMap(clientIp, "");
        //提取客票信息
        TicketInfoRequest ticketInfoRequest = new TicketInfoRequest("10", channelInfo.getOrderChannelCode(), channelInfo.getUserNo());
        ticketInfoRequest.setTicketNo(deliveryTicketInfoUser.getTicketNo());
        ticketInfoRequest.setCertType("TN");
        ticketInfoRequest.setPassengerName(passName);
        //提取客票信息
        try {
            TicketListInfoResponse ticketListInfoResponse = orderService.listTicketInfo(ticketInfoRequest, headMap);
            if (ticketListInfoResponse == null) {
                ServiceException.fail("未查询到匹配的行程数据");
            }
            boolean segmentMatchFlag = ticketListInfoResponse.getIBETicketInfoList().stream().anyMatch(
                    ticketInfo ->
                            ticketInfo.getSegmentInfoList().stream().anyMatch(
                                    segmentDetail -> {
                                        ApiAirPortInfoDto arrAirportInfo = cacheService.getLocalAirport(segmentDetail.getArrAirportCode());
                                        if (arrAirportInfo == null) {
                                            return false;
                                        }
                                        ApiAirPortInfoDto depAirportInfo = cacheService.getLocalAirport(segmentDetail.getDepAirportCode());
                                        if (depAirportInfo == null) {
                                            return false;
                                        }
                                        return (baseReq.getData().getDepCityCode().equalsIgnoreCase(depAirportInfo.getCityCode())
                                                && baseReq.getData().getArrCityCode().equalsIgnoreCase(arrAirportInfo.getCityCode())
                                                && baseReq.getData().getFlightNo().equalsIgnoreCase(segmentDetail.getFlightNo())
                                                && baseReq.getData().getFlightDate().equalsIgnoreCase(segmentDetail.getDepTime().substring(0, 10)));

                                    }

                            ));
            if (!segmentMatchFlag) {
                ServiceException.fail("未查询到匹配的行程数据");
            }

            List<DeliveryTicketInfo> deliveryTicketInfoList = new ArrayList<>();
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                //客票查询计数操作
                commonService.countOperation(clientIp, "TRIP_TICKET", "", limitCount);
                if (CollectionUtils.isNotEmpty(ticketListInfoResponse.getIBETicketInfoList())) {
                    for (PtIBETicketInfo ptIBETicketInfo : ticketListInfoResponse.getIBETicketInfoList()) {
                        //yyyy-MM-dd HH:mm
                        String curDateStr = DateUtil.getCurrentDateTimeStr();
                        Date curDate = DateUtil.toDate(curDateStr, "yyyy-MM-dd HH:mm");
                        //姓名匹配
                        String patternStr = passName + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*)";//正则表达式
                        Pattern pattern = Pattern.compile(patternStr);
                        Matcher matcher = pattern.matcher(ptIBETicketInfo.getPassengerName());
                        if (matcher.matches()) {
                            //自营客票判断
                            PtOwnSaleRequestDto ptOwnSaleRequestDto = new PtOwnSaleRequestDto("10", channelInfo.getOrderChannelCode(), channelInfo.getUserNo(), "");
                            String ticketNo = ptIBETicketInfo.getTicketNo();
                            if (ticketNo.indexOf("-") == -1) {
                                ticketNo = ptIBETicketInfo.getTicketNo().substring(0, 3) + "-" + ptIBETicketInfo.getTicketNo().substring(3);
                            }
                            ptOwnSaleRequestDto.setTicketNo(ticketNo);
                            //自营渠道划分
                            String allowChannelCodes = mainPageConfig.getTripCertChannelCodes();
                            String[] channelCodes = allowChannelCodes.split(",");
                            List<String> channelCodeList = new ArrayList<>(Arrays.asList(channelCodes));
                            ptOwnSaleRequestDto.setChannelCodes(channelCodeList);
                            PtOwnSaleResponseDto ptOwnSaleResponseDto = orderService.queryOwnSaleTicketInfo(ptOwnSaleRequestDto, headMap);
                            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptOwnSaleResponseDto.getResultCode())) {
                                if (!ptOwnSaleResponseDto.isOwnSale() && !"N".equalsIgnoreCase(mainPageConfig.getTripCertOwnSaleCheck())) {
                                    throw ServiceException.fail("非自营客票请联系出票方打印行程单！");
                                }
                            } else {
                                resp.setCode(WSEnum.ERROR.getResultCode());
                                resp.setMessage(ptOwnSaleResponseDto.getErrorInfo());
                                return resp;
                            }

                            //团队票检验  签注项中有GV标志，认为是团体票
                            if (!StringUtil.isNullOrEmpty(ptIBETicketInfo.getSigningInfo()) && ptIBETicketInfo.getSigningInfo().indexOf("GV") > -1) {
                                throw ServiceException.fail("团队票不支持邮寄行程单！");
                            }
                            if ("Y".equalsIgnoreCase(ptIBETicketInfo.getIsReceiptPrinted()) || "true".equalsIgnoreCase(ptIBETicketInfo.getIsReceiptPrinted())) {
                                throw ServiceException.fail("您已打印过行程单，请勿重复提交");
                            }
                            //行程结束检验
                            int usedCount = 0, openCount = 0;
                            //最后的使用日期
                            String lastFlightArrTime = curDateStr;
                            ApiAirPortInfoDto airPortInfo = null;
                            for (PtSegmentInfo ptSegmentInfo : ptIBETicketInfo.getSegmentInfoList()) {
                                if (TicketStateEnum.USED_FLOWN.equals(ptSegmentInfo.getTicketStatus())) {
                                    usedCount++;
                                    lastFlightArrTime = ptSegmentInfo.getArrTime();
                                    airPortInfo = cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode());
                                }
                                if (TicketStateEnum.OPEN_FOR_USE.equals(ptSegmentInfo.getTicketStatus()) || TicketStateEnum.CHECKED_IN.equals(ptSegmentInfo.getTicketStatus())) {
                                    openCount++;
                                }
                            }
                            if (usedCount == 0 && openCount == 0) {
                                throw ServiceException.fail("当前客票不符合邮寄条件！");
                            }
                            if (openCount > 0) {
                                throw ServiceException.fail("您尚未出行无法打印行程单！");
                            }
                            if (usedCount == 0) {
                                throw ServiceException.fail("无有效的出行行程！");
                            }
                            Date curArrDateTime = DateUtil.toTargetDate(lastFlightArrTime,
                                    Optional.ofNullable(airPortInfo).map(i -> i.getCityCode())
                                            .map(i -> cacheService.getLocalCity(i))
                                            .map(ApiCityInfoDto::getCityTimeZone)
                                            .orElse("8"),
                                    "8");
                            Date endDate = DateUtil.addOrLessDay(curArrDateTime, 28);
                            if (curDate.before(curArrDateTime)) {
                                throw ServiceException.fail("请于航班结束次日提交行程单申请");
                            }
                            if (curDate.after(endDate)) {
                                throw ServiceException.fail("客票已超过有效期，请联系客服办理");
                            }

                            DeliveryTicketInfo deliveryTicketInfo = new DeliveryTicketInfo();
                            ticketNo = deliveryTicketInfoUser.getTicketNo().indexOf("-") > -1 ? deliveryTicketInfoUser.getTicketNo().replace("-", "") : deliveryTicketInfoUser.getTicketNo();
                            deliveryTicketInfo.setTicketNo(ticketNo);
                            deliveryTicketInfo.setPassName(deliveryTicketInfoUser.getPassName());
                            deliveryTicketInfo.setCheckFlag(EncoderHandler.encodeByMD5(ticketNo + deliveryTicketInfoUser.getPassName()));
                            //航段信息处理
                            List<PassageInfo> passageInfoList = new ArrayList<>();
                            ptIBETicketInfo.getSegmentInfoList().stream().forEach(ptSegmentInfo -> {
                                PassageInfo passageInfo = new PassageInfo();
                                passageInfo.setFlightNo(ptSegmentInfo.getFlightNo());
                                passageInfo.setDepTime(ptSegmentInfo.getDepTime());
                                passageInfo.setArrTime(ptSegmentInfo.getArrTime());
                                passageInfo.setDepAirPortCode(ptSegmentInfo.getDepAirportCode());

                                passageInfo.setDepAirPortName(Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(i -> i.getAirPortName()).orElse(null));
                                passageInfo.setArrAirPortCode(ptSegmentInfo.getArrAirportCode());
                                passageInfo.setArrAirPortName(
                                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(i -> i.getAirPortName()).orElse(null)
                                );
                                //城市名称
                                passageInfo.setDepCityName(
                                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(i -> i.getCityName()).orElse(null));
                                passageInfo.setArrCityName(
                                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(i -> i.getCityName()).orElse(null));
                                passageInfo.setDepTerminal(FlightUtil.formatTerminal(Optional.ofNullable(ptSegmentInfo.getDepAirportTerminal()).orElse(null))
                                );
                                passageInfo.setArrTerminal(
                                        FlightUtil.formatTerminal(Optional.ofNullable(ptSegmentInfo.getArrAirportTerminal()).orElse(null)));
                                passageInfo.setDepCityCode(Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(i -> i.getCityCode()).orElse(null));
                                passageInfo.setArrCityCode(Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(i -> i.getCityCode()).orElse(null));
                                passageInfoList.add(passageInfo);
                            });
                            deliveryTicketInfo.setPassageInfoList(passageInfoList);
                            deliveryTicketInfoList.add(deliveryTicketInfo);

                            //处理改期客票（判断客票为已改期且符合行程单打印标准的客票，若原票未打印行程单，则前端取改期后客票和改期前原票展示；若原票已打印行程单，则仅展示改期后客票）
                            if (StringUtils.isNotBlank(ptIBETicketInfo.getExchangeInfo())) {
                                Pattern tickectPattern = Pattern.compile(PatternCommon.TICKET_NO);
                                Matcher tickectMatcher = tickectPattern.matcher(ptIBETicketInfo.getExchangeInfo());
                                if (tickectMatcher.matches()) {
                                    ticketInfoRequest.setTicketNo(ptIBETicketInfo.getExchangeInfo());
                                    ticketInfoRequest.setPassengerName(passName);
                                    processExchangeInfo(ticketInfoRequest, headMap, deliveryTicketInfoUser, deliveryTicketInfoList, channelCode, userNo);
                                }
                            }
                        } else {
                            resp.setMessage("未查询到对应的航班信息！");
                            resp.setCode(WSEnum.ERROR.getResultCode());
                            return resp;
                        }
                    }
                }
            } else {
                resp.setCode(WSEnum.ERROR.getResultCode());
                resp.setMessage(ticketListInfoResponse.getErrorInfo());
                return resp;
            }
            if (CollectionUtils.isEmpty(deliveryTicketInfoList)) {
                throw ServiceException.fail("无符合条件的客票信息！");
            } else {
                resp.setData(deliveryTicketInfoList);
                return resp;
            }
        } catch (Exception e) {
            String exMsg = "";
            if (e instanceof ServiceException) {
                exMsg = ((ServiceException) e).getError().getMessage();
            } else {
                exMsg = WSEnum.ERROR.getResultInfo();
            }
            log.error("邮寄行程单客票验证异常:{}", exMsg);
            throw ServiceException.fail(exMsg);
        }
    }

    /**
     * 下单请求参数
     *
     * @param deliveryBooking
     * @param channelCode
     * @param userNo
     * @return
     */
    private CouponProductBuyRequestDto createCouponProBuyReq(DeliveryBooking deliveryBooking, String channelCode, String userNo) {
        String ffpId = ServiceContext.getHead().ffpId;
        String ffpCardNo = ServiceContext.getHead().ffpNo;

        CouponProductBuyRequestDto couponProductBuyRequest = new CouponProductBuyRequestDto();
        couponProductBuyRequest.version = "10";
        couponProductBuyRequest.channelCode = "MOBILE";
        couponProductBuyRequest.setUserNo(userNo);
        couponProductBuyRequest.setChannelOrderNo(deliveryBooking.getChannelOrderNo());
        couponProductBuyRequest.setFfpId(ffpId);
        couponProductBuyRequest.setFfpCardNo(ffpCardNo);
        //邮寄行程单固定为MailTravel
        couponProductBuyRequest.setBuyType(VoucherTypesEnum.MAILTRAVEL.getCode());
        com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.MailTravelProduct mailTravelProduct = new MailTravelProduct();
        DeliveryAddress deliveryAddress = deliveryBooking.getDeliveryAddress();
        mailTravelProduct.setRecipientsAddress(deliveryAddress.getDeliverToProvinceName() + deliveryAddress.getDeliverToCityName() + deliveryAddress.getDetailAddress());
        mailTravelProduct.setRecipientsName(deliveryAddress.getLinker());
        mailTravelProduct.setRecipientsPhone(deliveryAddress.getHandphoneNumber());
        List<com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.PassengerItinerary> passengerItineraryList = new ArrayList<>();
        deliveryBooking.getDeliveryTicketInfoList().stream().forEach(deliveryTicketInfo -> {
            deliveryTicketInfo.getPassageInfoList().stream().forEach(passageInfo -> {
                com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.PassengerItinerary passengerItinerary = new com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.PassengerItinerary();
                passengerItinerary.setTicketNo(deliveryTicketInfo.getTicketNo());
                passengerItinerary.setPassengerName(deliveryTicketInfo.getPassName());
                passengerItinerary.setFlightNo(passageInfo.getFlightNo());
                passengerItinerary.setDeptAirport(passageInfo.getDepAirPortCode());
                passengerItinerary.setArrAirport(passageInfo.getArrAirPortCode());
                passengerItinerary.setDepDateTime(passageInfo.getDepTime());
                passengerItinerary.setArrDateTime(passageInfo.getArrTime());
                passengerItineraryList.add(passengerItinerary);
            });
        });
        mailTravelProduct.setPassengerItineraryList(passengerItineraryList);
        List<com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookProductInfo> bookProductInfoList = new ArrayList<>();
        com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookProductInfo productInfo = new com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookProductInfo();
        productInfo.setProductName("邮寄行程单");
        productInfo.setProductType(VoucherTypesEnum.MAILTRAVEL.getCode());
        List<com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookResourceInfo> bookResourceInfos = CollUtil.newArrayList();
        com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookResourceInfo bookResourceInfo = new com.juneyaoair.oneorder.order.dto.benefitorder.createorder.mailtravel.BookResourceInfo();
        bookResourceInfo.setResourceType(VoucherTypesEnum.MAILTRAVEL.getCode());
        bookResourceInfo.setMailTravelProduct(mailTravelProduct);
        bookResourceInfos.add(bookResourceInfo);

        productInfo.setResources(bookResourceInfos);
        bookProductInfoList.add(productInfo);
        couponProductBuyRequest.setProducts(bookProductInfoList);
        couponProductBuyRequest.setTotalPrice(new BigDecimal(deliveryBooking.getDeliveryFee().toString()));
        couponProductBuyRequest.setPayAmount(new BigDecimal(deliveryBooking.getDeliveryFee().toString()));
        couponProductBuyRequest.setUseScore(0);
        couponProductBuyRequest.setLinkerMobile(deliveryBooking.getDeliveryAddress().getHandphoneNumber());
        couponProductBuyRequest.setLinker(deliveryBooking.getDeliveryAddress().getLinker());
        return couponProductBuyRequest;
    }

    //处理改期客票信息
    private void processExchangeInfo(TicketInfoRequest ticketInfoRequest, Map<String, String> headMap,
                                     DeliveryTicketInfoUser deliveryTicketInfoUser, List<DeliveryTicketInfo> deliveryTicketInfoList,
                                     String channelCode, String userNo) {
        //提取客票信息
        try {
            TicketListInfoResponse ticketListInfoResponse = orderService.listTicketInfo(ticketInfoRequest, headMap);
            if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ticketListInfoResponse.getResultCode())) {
                for (PtIBETicketInfo ptIBETicketInfo : ticketListInfoResponse.getIBETicketInfoList()) {
                    checkExchangeTicketInfo(deliveryTicketInfoUser, ptIBETicketInfo, headMap, deliveryTicketInfoList, channelCode, userNo);
                }
            }
        } catch (Exception e) {
            log.error("改期原票获取客票信息异常", e);
        }
    }

    private void checkExchangeTicketInfo(DeliveryTicketInfoUser deliveryTicketInfoUser, PtIBETicketInfo ptIBETicketInfo, Map<String, String> headMap,
                                         List<DeliveryTicketInfo> deliveryTicketInfoList, String channelCode, String userNo) {
        //yyyy-MM-dd HH:mm
        String curDateStr = DateUtil.getCurrentDateTimeStr();
        Date curDate = DateUtil.toDate(curDateStr, "yyyy-MM-dd HH:mm");
        //姓名匹配
        String passName = deliveryTicketInfoUser.getPassName().toUpperCase();
        String patternStr = passName + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*)";//正则表达式
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(ptIBETicketInfo.getPassengerName());
        if (matcher.matches()) {
            //自营客票判断
            PtOwnSaleRequestDto ptOwnSaleRequestDto = new PtOwnSaleRequestDto("10", channelCode, userNo, "");
            String ticketNo = ptIBETicketInfo.getTicketNo();
            if (ticketNo.indexOf("-") == -1) {
                ticketNo = ptIBETicketInfo.getTicketNo().substring(0, 3) + "-" + ptIBETicketInfo.getTicketNo().substring(3);
            }
            ptOwnSaleRequestDto.setTicketNo(ticketNo);
            //自营渠道划分
            String allowChannelCodes = mainPageConfig.getTripCertChannelCodes();
            String[] channelCodes = allowChannelCodes.split(",");
            List<String> channelCodeList = new ArrayList<>(Arrays.asList(channelCodes));
            ptOwnSaleRequestDto.setChannelCodes(channelCodeList);
            try {
                PtOwnSaleResponseDto ptOwnSaleResponseDto = orderService.queryOwnSaleTicketInfo(ptOwnSaleRequestDto, headMap);
                if (UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(ptOwnSaleResponseDto.getResultCode())) {
                    if (!ptOwnSaleResponseDto.isOwnSale()) {
                        return;
                    }
                } else {
                    return;
                }
            } catch (Exception e) {
                log.error("改期原票自营客票判断异常", e);
                return;
            }
            //团队票检验  签注项中有GV标志，认为是团体票
            if (!StringUtil.isNullOrEmpty(ptIBETicketInfo.getSigningInfo()) && ptIBETicketInfo.getSigningInfo().indexOf("GV") > -1) {
                return;
            }
            if ("Y".equalsIgnoreCase(ptIBETicketInfo.getIsReceiptPrinted()) || "true".equalsIgnoreCase(ptIBETicketInfo.getIsReceiptPrinted())) {
                return;
            }
            //行程结束检验
            int exchangedCount = 0;
            //最后的使用日期
            String lastFlightArrTime = curDateStr;
            ApiAirPortInfoDto airPortInfo = null;
            for (PtSegmentInfo ptSegmentInfo : ptIBETicketInfo.getSegmentInfoList()) {
                if (TicketStateEnum.EXCHANGED.equals(ptSegmentInfo.getTicketStatus())) {
                    exchangedCount++;
                    lastFlightArrTime = ptSegmentInfo.getArrTime();
                    airPortInfo =
                            Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).orElse(null);
                }
            }
            //为测试方便，此处测试时暂时注释
            if (exchangedCount == 0) {
                return;
            }
            Date curArrDateTime = DateUtil.toTargetDate(lastFlightArrTime,
                    Optional.ofNullable(airPortInfo).map(i -> cacheService.getLocalCity(i.getCityCode())).map(ApiCityInfoDto::getCityTimeZone).orElse("8"), "8");
            Date endDate = DateUtil.addOrLessDay(curArrDateTime, 28);
            if (curDate.before(curArrDateTime) || curDate.after(endDate)) {
                return;
            }
            DeliveryTicketInfo deliveryTicketInfo = new DeliveryTicketInfo();
            ticketNo = ticketNo.indexOf("-") > -1 ? ticketNo.replace("-", "") : ticketNo;
            deliveryTicketInfo.setTicketNo(ticketNo);
            deliveryTicketInfo.setPassName(deliveryTicketInfoUser.getPassName());
            deliveryTicketInfo.setCheckFlag(EncoderHandler.encodeByMD5(ticketNo + deliveryTicketInfoUser.getPassName()));
            //航段信息处理
            List<PassageInfo> passageInfoList = new ArrayList<>();
            ptIBETicketInfo.getSegmentInfoList().stream().forEach(ptSegmentInfo -> {
                PassageInfo passageInfo = new PassageInfo();
                passageInfo.setFlightNo(ptSegmentInfo.getFlightNo());
                passageInfo.setDepTime(ptSegmentInfo.getDepTime());
                passageInfo.setArrTime(ptSegmentInfo.getArrTime());
                passageInfo.setDepAirPortCode(ptSegmentInfo.getDepAirportCode());

                passageInfo.setDepAirPortName(Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(i -> i.getAirPortName()).orElse(null));
                passageInfo.setArrAirPortCode(ptSegmentInfo.getArrAirportCode());
                passageInfo.setArrAirPortName(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(i -> i.getAirPortName()).orElse(null)
                );
                //城市名称
                passageInfo.setDepCityName(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportCode())).map(i -> i.getAirPortName()).orElse(null));
                passageInfo.setArrCityName(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportCode())).map(i -> i.getAirPortName()).orElse(null));
                passageInfo.setDepTerminal(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getDepAirportTerminal())).map(i -> i.getAirPortName()).orElse(null));
                passageInfo.setArrTerminal(
                        Optional.ofNullable(cacheService.getLocalAirport(ptSegmentInfo.getArrAirportTerminal())).map(i -> i.getAirPortName()).orElse(null));
                passageInfoList.add(passageInfo);
                passageInfoList.add(passageInfo);
            });
            deliveryTicketInfo.setPassageInfoList(passageInfoList);
            deliveryTicketInfoList.add(deliveryTicketInfo);
        }
    }

}
