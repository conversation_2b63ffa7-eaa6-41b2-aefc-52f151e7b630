package com.juneyaoair.oneorder.mainpage.controller;

import com.juneyaoair.horder.dto.electronic.ElectronQueryDTO;
import com.juneyaoair.horder.dto.electronic.result.CancelResult;
import com.juneyaoair.horder.dto.electronic.result.PrintResult;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicCancelPostParam;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicEmailParam;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicInfoResult;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicPrintPostParam;
import com.juneyaoair.oneorder.mainpage.service.ElectronicItineraryService;
import com.juneyaoair.oneorder.page.PageResult;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description 新版电子行程及发票服务
 * @created 2024/5/8 15:42
 */
@RestController
@Api(value = "ElectronicItineraryController",tags = "新版电子行程及发票服务")
public class ElectronicItineraryController {

    @Autowired
    private ElectronicItineraryService electronicItineraryService;

    @ApiOperation(value = "查询可开具电子行程单开具清单接口")
    @PostMapping("/electronicItinerary/queryElectronic")
    public ResponseData<PageResult<ElectronicInfoResult>> queryElectronic(@Validated @RequestBody RequestData<ElectronQueryDTO> requestData){
        ElectronQueryDTO electronQuery = requestData.getData();
        // 查询证件号、姓名全部为空 基于会员查询
       if (!StringUtils.isAllBlank(electronQuery.getSearchNo(), electronQuery.getPassengerName())) {
             // 查询证件号、姓名存在空
            if (StringUtils.isAnyBlank(electronQuery.getSearchNo(), electronQuery.getPassengerName())) {
                throw ServiceException.fail("乘机证件号/票号，乘机人姓名不能为空");
            }
        }
        electronQuery.setFfpId(requestData.getFfpId());
        electronQuery.setChannelNo(requestData.getChannelNo());
        PageResult<ElectronicInfoResult> electronicInfoPageInfo = electronicItineraryService.queryElectronic(requestData.getFfpNo(), electronQuery);
        return ResponseData.suc(electronicInfoPageInfo);
    }

    @ApiOperation(value = "电子发票冲红接口")
    @PostMapping("/electronicItinerary/cancel")
    public ResponseData<CancelResult> cancel(@Validated @RequestBody RequestData<ElectronicCancelPostParam> requestData){
        CancelResult cancelResult = electronicItineraryService.cancel(requestData.getFfpNo(), requestData.getChannelNo(), requestData.getOriginIp(), requestData.getData());
        return ResponseData.suc(cancelResult);
    }

    @ApiOperation(value = "电子行程单开具接口")
    @PostMapping("/electronicItinerary/print")
    public ResponseData<PrintResult> print(@Validated @RequestBody RequestData<ElectronicPrintPostParam> requestData){
        PrintResult printResult = electronicItineraryService.print(requestData.getFfpNo(), requestData.getChannelNo(), requestData.getOriginIp(), requestData.getData());
        return ResponseData.suc(printResult);
    }

    @ApiOperation(value = "电子行程单重推接口")
    @PostMapping("/electronicItinerary/rePush")
    public ResponseData<String> rePush(@Validated @RequestBody RequestData<ElectronicEmailParam> requestData){
        electronicItineraryService.rePush(requestData.getFfpNo(), requestData.getChannelNo(), requestData.getOriginIp(), requestData.getData());
        return ResponseData.suc();
    }

}
