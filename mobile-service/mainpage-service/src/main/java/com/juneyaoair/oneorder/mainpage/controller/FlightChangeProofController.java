package com.juneyaoair.oneorder.mainpage.controller;


import com.juneyaoair.oneorder.annotation.ApiLog;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofApply;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofResp;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofSendInfo;
import com.juneyaoair.oneorder.mainpage.service.IFlightChangeProofService;
import com.juneyaoair.oneorder.mainpage.service.impl.VerifyFlightDelayCodeReq;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Api(value = "FlightChangeElectronicProofController", tags = "航延证明服务")
@RequestMapping("/electronicproof")
@RestController
@Slf4j
public class FlightChangeProofController {
    @Resource
    IFlightChangeProofService flightChangeProofService;

    @ApiOperation(value = "申请航变证明", notes = "申请航变证明")
    @RequestMapping(value = "/applyFlightChangeProof", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiLog
    public ResponseData<List<FlightChangeProofResp>> applyFlightChangeProof(@RequestBody @Validated RequestData<FlightChangeProofApply> req,
                                                                            HttpServletRequest request) {
        return  flightChangeProofService.applyFlightChangeProof(req, request);
    }

    @ApiOperation(value = "发送航变证明", notes = "发送航变证明邮件(或短信)")
    @RequestMapping(value = "sendFlightChangeProof", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    @ApiLog
    public ResponseData<List<String>> sendFlightChangeProof(@RequestBody RequestData<FlightChangeProofSendInfo> req,
                                                               HttpServletRequest request, HttpServletResponse response) {
        return flightChangeProofService.sendFlightChangeProof(req, request,response);
    }

    @ApiOperation(value = "航班延误证明验真", notes = "航班延误证明验真")
    @RequestMapping(value = "verifyFlightDelayCode", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public ResponseData<Boolean> verifyFlightDelayCode(@RequestBody RequestData<VerifyFlightDelayCodeReq> req, HttpServletRequest request, BindingResult bindingResult) {
        return flightChangeProofService.verifyFlightDelayCode(req, request, bindingResult);
    }
}
