package com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf;

import com.juneyaoair.oneorder.mainpage.constant.MainPageConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;

import java.util.Optional;

@Component
public class ItineraryThymeleafChineseProcessor extends AbstractThymeleafProcessor {
    @Override
    protected void prepareDiffData(ItineraryContext context) {
        //中文版本的电子邮件
        context.getedDataContext.template = MainPageConstant.TRAVEL_ITINERARY_EMAIL_TEMPLATE_PATH;
    }

    @Override
    protected String templateProcess(ItineraryContext context) {
        Context thyContext = new Context();
        thyContext.setVariable("pnr", context.flightItineraryInfo.getDetrSegmentDetailList().get(0).getPnrNo() == null ? "" : context.flightItineraryInfo.getDetrSegmentDetailList().get(0).getPnrNo());
        thyContext.setVariable("passengerName", context.flightItineraryInfo.getPassengerName());
        thyContext.setVariable("ticketNo", StringUtils.isBlank(context.flightItineraryInfo.getFollowTicketNo()) ? context.flightItineraryInfo.getTicketNo() : "");
        thyContext.setVariable("followTicketNo", context.flightItineraryInfo.getFollowTicketNo());
        thyContext.setVariable("certNo", context.getedDataContext.certNo);
        thyContext.setVariable("issueDate", context.flightItineraryInfo.getIssueDate());
        thyContext.setVariable("iataNo", context.flightItineraryInfo.getIataNo());
        thyContext.setVariable("payMethod", context.flightItineraryInfo.getPayMethod());
        thyContext.setVariable("tax", context.getedDataContext.tax);
        thyContext.setVariable("fare", context.getedDataContext.fare);
        thyContext.setVariable("totalAmount", context.getedDataContext.totalAmount);
        thyContext.setVariable("signingInfo", Optional.ofNullable(context.flightItineraryInfo.getSigningInfo()).orElse(""));
        thyContext.setVariable("segments", context.getedDataContext.segmentInfos);
        thyContext.setVariable("hoLogo",context.hoLogo);
        thyContext.setVariable("iataLogo",context.iataLogo);
        return templateEngine.process(context.getedDataContext.template, thyContext);
    }
}
