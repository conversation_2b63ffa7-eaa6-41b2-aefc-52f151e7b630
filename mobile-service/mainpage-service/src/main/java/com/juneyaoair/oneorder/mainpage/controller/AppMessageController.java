package com.juneyaoair.oneorder.mainpage.controller;

import com.juneyaoair.flightbasic.api.index.Mess;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.request.push.PushDetailQuery;
import com.juneyaoair.flightbasic.request.push.PushDetailUpdateState;
import com.juneyaoair.flightbasic.utils.BaseRequestUtil;
import com.juneyaoair.flightbasic.utils.BaseResultUtil;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.mainpage.dto.homepage.getPersonMsg.MessageStatusReq;
import com.juneyaoair.oneorder.mainpage.dto.homepage.getPersonMsg.PersonalMsgReq;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 * @description
 * @date 2024/01/31 10:43
 */
@Slf4j
@Api(value = "AppMessageController", tags = "APP消息服务")
@RequestMapping("/appMessage")
@RestController
public class AppMessageController extends BaseController {

    @Resource
    private FlightBasicProviderClient flightBasicProviderClient;

    @ApiOperation(value = "获取用户个人APP消息", notes = "获取用户个人APP消息")
    @PostMapping("/getPersonMsg")
    public ResponseData<List<Mess>> getPersonMsg(@RequestBody @Validated RequestData<PersonalMsgReq> requestData) {
        PushDetailQuery pushDetailQuery = new PushDetailQuery();
        pushDetailQuery.setFfpId(requestData.getFfpId());
        pushDetailQuery.setFfpCardNo(requestData.getFfpNo());
        pushDetailQuery.setMessageType("CUSTOM");
        PersonalMsgReq personMsg = requestData.getData();
        pushDetailQuery.setAppMessageTag(personMsg.getAppMessageTag());
        Date date = new Date();
        Date beforDate = DateUtil.addOrLessMonth(date, -3);
        String startDate = DateUtil.getDateStringAllDate(beforDate);
        pushDetailQuery.setStartDate(startDate);
        Date furDate = DateUtil.addOrLessDay(date, 1);
        String endDate = DateUtil.getDateStringAllDate(furDate);
        pushDetailQuery.setEndDate(endDate);
        BaseResultDTO<List<Mess>> pushDetailResult = flightBasicProviderClient.getPushDetailList(BaseRequestUtil.createRequest(pushDetailQuery, requestData.getChannelNo()));
        List<Mess> messList = BaseResultUtil.getResult(pushDetailResult);
        return ResponseData.suc(messList);
    }

    @ApiOperation(value = "获取未读消息的数量", notes = "获取未读消息的数量")
    @PostMapping(value = "/getNoReadNum")
    public ResponseData<Long> getNoReadNum(@RequestBody @Validated RequestData<PersonalMsgReq> requestData) {
        PushDetailQuery pushDetailQuery = new PushDetailQuery();
        pushDetailQuery.setFfpId(requestData.getFfpId());
        pushDetailQuery.setFfpCardNo(requestData.getFfpNo());
        pushDetailQuery.setMessageType("CUSTOM");
        PersonalMsgReq personMsg = requestData.getData();
        pushDetailQuery.setAppMessageTag(personMsg.getAppMessageTag());
        Date date = new Date();
        Date beforDate = DateUtil.addOrLessMonth(date, -3);
        String startDate = DateUtil.getDateStringAllDate(beforDate);
        pushDetailQuery.setStartDate(startDate);
        Date furDate = DateUtil.addOrLessDay(date, 1);
        String endDate = DateUtil.getDateStringAllDate(furDate);
        pushDetailQuery.setEndDate(endDate);
        BaseResultDTO<Long> noReadNumResult = flightBasicProviderClient.getNoReadNum(BaseRequestUtil.createRequest(pushDetailQuery, requestData.getChannelNo()));
        Long noReadNum = BaseResultUtil.getResult(noReadNumResult);
        return ResponseData.suc(noReadNum);
    }

    @ApiOperation(value = "个人消息置已读", notes = "个人消息置已读")
    @PostMapping(value = "setPersonalMsgReadStatus")
    public ResponseData setPersonalMsgReadStatus(@RequestBody @Validated RequestData<MessageStatusReq> requestData) {
        PushDetailUpdateState pushDetailUpdateState = new PushDetailUpdateState();
        pushDetailUpdateState.setFfpId(requestData.getFfpId());
        pushDetailUpdateState.setFfpCardNo(requestData.getFfpNo());
        pushDetailUpdateState.setMessageType("CUSTOM");
        pushDetailUpdateState.setMessageIds(requestData.getData().getMessageIds());
        pushDetailUpdateState.setState("Y");
        BaseResultDTO<Object> baseResult = flightBasicProviderClient.updateState(BaseRequestUtil.createRequest(pushDetailUpdateState, requestData.getChannelNo()));
        BaseResultUtil.getResult(baseResult);
        return ResponseData.suc();
    }

    @ApiOperation(value = "删除个人消息", notes = "删除个人消息")
    @PostMapping(value = "delPersonalMsg")
    public ResponseData delPersonalMsg(@RequestBody @Validated RequestData<MessageStatusReq> requestData) {
        Set<String> messageIds = requestData.getData().getMessageIds();
        if (CollectionUtils.isEmpty(messageIds)) {
            throw new ServiceException("消息ID不能为空");
        }
        PushDetailUpdateState pushDetailUpdateState = new PushDetailUpdateState();
        pushDetailUpdateState.setFfpId(requestData.getFfpId());
        pushDetailUpdateState.setFfpCardNo(requestData.getFfpNo());
        pushDetailUpdateState.setMessageType("CUSTOM");
        pushDetailUpdateState.setMessageIds(messageIds);
        pushDetailUpdateState.setState("D");
        BaseResultDTO<Object> baseResult = flightBasicProviderClient.updateState(BaseRequestUtil.createRequest(pushDetailUpdateState, requestData.getChannelNo()));
        BaseResultUtil.getResult(baseResult);
        return ResponseData.suc();
    }

}
