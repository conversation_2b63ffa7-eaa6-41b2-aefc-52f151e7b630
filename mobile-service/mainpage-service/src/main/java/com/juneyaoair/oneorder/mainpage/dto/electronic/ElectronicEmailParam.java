package com.juneyaoair.oneorder.mainpage.dto.electronic;

import com.juneyaoair.horder.dto.electronic.post.ElectronicEmailDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description 电子行程单-手动发送邮件
 * @created 2024/5/13 17:24
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ElectronicEmailParam extends ElectronicEmailDto {

    @NotBlank(message = "签名信息不能为空")
    @ApiModelProperty(value = "签名", required = true)
    private String signature;

}
