package com.juneyaoair.oneorder.mainpage.dto.lostitems;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;

@ApiModel
@XmlRootElement(name = "LostItemsApplyRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class LostItemsApplyRequest {
    @ApiModelProperty("姓名")
    @NotEmpty(message = "姓名")
    private String reporterName;
    @ApiModelProperty("手机号码")
    @NotEmpty(message = "手机号码")
    @Size(min = 6, max = 15, message = "手机号码格式不对")
    private String reporterContact;
    @ApiModelProperty("goodsId")
    private String goodsId;
    @ApiModelProperty("座位号")
    private String seatNo;
    @ApiModelProperty("备注")
    private String lbRemark;

    /**
     * 证件号码
     */
    @ApiModelProperty("证件号码")
    private String passIdcard;

    /**
     * 证件类别
     */
    @ApiModelProperty("证件类别")
    private String passIdcardType;

    public String getReporterName() {
        return reporterName;
    }

    public void setReporterName(String reporterName) {
        this.reporterName = reporterName;
    }

    public String getReporterContact() {
        return reporterContact;
    }

    public void setReporterContact(String reporterContact) {
        this.reporterContact = reporterContact;
    }

    public String getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(String goodsId) {
        this.goodsId = goodsId;
    }

    public String getSeatNo() {
        return seatNo;
    }

    public void setSeatNo(String seatNo) {
        this.seatNo = seatNo;
    }

    public String getLbRemark() {
        return lbRemark;
    }

    public void setLbRemark(String lbRemark) {
        this.lbRemark = lbRemark;
    }

    public String getPassIdcard() {
        return passIdcard;
    }

    public void setPassIdcard(String passIdcard) {
        this.passIdcard = passIdcard;
    }

    public String getPassIdcardType() {
        return passIdcardType;
    }

    public void setPassIdcardType(String passIdcardType) {
        this.passIdcardType = passIdcardType;
    }
}
