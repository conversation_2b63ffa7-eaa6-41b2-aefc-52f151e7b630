package com.juneyaoair.oneorder.mainpage.dto.homepage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel
@Data
public class GetWechatQRCodeBffReq {

    @ApiModelProperty(value = "scene",notes = "传到二维码中")
    public String scene;
    @ApiModelProperty(value = "sourceType",notes = "需要找开发配置")//映射的page在flightBasic配置
    public String sourceType;
}
