package com.juneyaoair.oneorder.mainpage.dto.changeproof;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Data
public class FlightChangeProofSendInfo {
    /**
     * 收件人邮箱
     */
    @ApiModelProperty("收件人邮箱")
    private String toMail;
    /**
     * 邮件类型
     * sendFlightChange
     */
    @ApiModelProperty("邮件类型 sendFlightChange")
    private String type;
    /**
     * 航班信息
     */
    @ApiModelProperty("航班信息")
    private List<FlightChangeProofResp> proofResps;
    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String telephone;
    /**
     * 短信类型
     * flightchangeSms
     */
    @ApiModelProperty("短信类型 flightchangeSms")
    private String smsType;

}
