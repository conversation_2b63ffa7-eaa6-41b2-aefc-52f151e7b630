package com.juneyaoair.oneorder.mainpage.mapstruct;

import com.juneyaoair.flightbasic.advertisement.AdvertisementDto;
import com.juneyaoair.flightbasic.request.wechat.SpecialAirlineDTO;
import com.juneyaoair.oneorder.mainpage.dto.SpecialAirLine;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/18 17:12
 */
@Mapper
public interface SpecialAirlineDTOMapstruct {
    SpecialAirlineDTOMapstruct mapper = Mappers.getMapper(SpecialAirlineDTOMapstruct.class);
    List<SpecialAirLine> toSpecialAirLineList(List<SpecialAirlineDTO> specialAirlineDTOList);


    @Mappings({
            @Mapping(source = "price", target = "lowPrice")
    })
    SpecialAirLine toSpecialAirLine(SpecialAirlineDTO specialAirlineDTO);

}
