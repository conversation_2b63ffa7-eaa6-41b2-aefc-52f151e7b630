package com.juneyaoair.oneorder.mainpage.dto.tripcert;

import com.juneyaoair.oneorder.constant.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * @description  行程客票信息
 */
@ApiModel(description = "行程客票信息")
@Data
public class DeliveryTicketInfo {
    @ApiModelProperty("客票信息")
    @NotBlank(message = "客票信息不能为空")
    @Pattern(regexp = PatternCommon.TICKET_NO,message = "请输入正确的票号")
    private String ticketNo;
    @ApiModelProperty("乘机人姓名")
    @NotBlank(message = "乘机人姓名不能为空")
    private String passName;
    @ApiModelProperty("验证信息")
    @NotBlank(message = "验证信息不能为空")
    private String checkFlag;
    @ApiModelProperty(value = "航段信息", required = true)
    @NotNull(message = "航段信息不能为空")
    @Size(min = 1,message = "至少包含一段航段信息")
    private List<PassageInfo> passageInfoList;//航段信息
}
