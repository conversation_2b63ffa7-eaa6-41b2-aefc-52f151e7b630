package com.juneyaoair.oneorder.mainpage.controller;

import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.mainpage.dto.cfc.CFCModuleRequest;
import com.juneyaoair.oneorder.mainpage.dto.cfc.NearFlightRecommend;
import com.juneyaoair.oneorder.mainpage.service.impl.FlightFareServiceImpl;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@Slf4j
@RequestMapping("/flight")
@RestController
@Api(value = "航班查询-额外航班信息", tags = {"航班查询-额外航班信息"})
public class FlightFareController {

    @Autowired
    private FlightFareServiceImpl flightFareService;

    @ApiOperation(value = "航班查询无结果页-邻近航线、热门推荐-模块", notes = "返回后台共飞模块配置的-邻近航线、热门推荐-类型的航班")
    @RequestMapping(value = "/cfcModule", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public ResponseData<List<NearFlightRecommend>> getCFCAirRoute(@RequestBody @Validated RequestDataDto<CFCModuleRequest> cfcModuleRequest) {
        return ResponseData.success(flightFareService.getCFCAirRoute(cfcModuleRequest));
    }

}
