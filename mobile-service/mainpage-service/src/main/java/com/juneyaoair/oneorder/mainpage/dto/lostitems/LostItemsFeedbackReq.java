package com.juneyaoair.oneorder.mainpage.dto.lostitems;


import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "LostItemsApplyRequest")
@XmlAccessorType(XmlAccessType.FIELD)
public class LostItemsFeedbackReq extends LostItemsApplyRequest {
    @NotEmpty(message = "航班号不能为空")
    @Size(min = 6, max = 6, message = "航班号格式不对")
    private String rlFlightNo;
    @NotEmpty(message = "航班日期不能为空")
    private String flightDate;
    @NotEmpty(message = "遗失地点不能为空")
    private String lossPlace;

    private String leaveBehindLevelNew;

    public String getLeaveBehindLevelNew() {
        return leaveBehindLevelNew;
    }

    public void setLeaveBehindLevelNew(String leaveBehindLevelNew) {
        this.leaveBehindLevelNew = leaveBehindLevelNew;
    }

    private String filePath;

    private List<String> fileUrls;

    public String getRlFlightNo() {
        return rlFlightNo;
    }

    public void setRlFlightNo(String rlFlightNo) {
        this.rlFlightNo = rlFlightNo;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getLossPlace() {
        return lossPlace;
    }

    public void setLossPlace(String lossPlace) {
        this.lossPlace = lossPlace;
    }


    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public List<String> getFileUrls() {
        return fileUrls;
    }

    public void setFileUrls(List<String> fileUrls) {
        this.fileUrls = fileUrls;
    }
}
