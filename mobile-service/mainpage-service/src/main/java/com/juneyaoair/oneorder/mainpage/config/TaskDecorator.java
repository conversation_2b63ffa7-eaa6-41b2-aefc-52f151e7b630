package com.juneyaoair.oneorder.mainpage.config;

import brave.ScopedSpan;
import brave.Tracer;
import brave.Tracing;
import brave.propagation.TraceContext;
import com.juneyaoair.oneorder.common.concurrency.HOContext;
import com.juneyaoair.oneorder.common.concurrency.HOTaskDecorator;
import com.juneyaoair.oneorder.common.concurrency.ParaTask;
import com.juneyaoair.oneorder.common.util.SpringContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class TaskDecorator implements HOTaskDecorator {
    @Autowired(required = false)
    Tracing tracing;


    //请勿包装为futrure
    @Override
    public Runnable decorate(Runnable runnable) {
        try {
            ScopedSpan span = null;
            try {
                if (tracing != null) {
                    //参考TraceRunnable写法
                    Tracer tracer = tracing.tracer();
                    TraceContext parent = tracing.currentTraceContext().get();
                    span = tracer.startScopedSpanWithParent("defaultSpanName",
                            parent);
                }
            } catch (Exception e) {
                //
            }
            HOContext context = HOContext.getContext();
            ScopedSpan finalSpan = span;
            return () -> {
                try {
                    if (context == null) {
                        HOContext.setContext(new HOContext());
                    } else {
                        HOContext.setContext(context);
                    }
                } catch (Exception e) {
                    if (finalSpan != null) {
                        finalSpan.error(e);
                    }
                    throw e;
                } finally {
                    HOContext.remove();
                    Optional.ofNullable(finalSpan).ifPresent(ScopedSpan::finish);
                }
            };
        } catch (Exception e) {
            return runnable;
        }
    }

    @Override
    public <T> ParaTask<T> decorate(ParaTask<T> runnable) {
        try {
            ScopedSpan span = null;
            try {
                //参考TraceRunnable写法
                Tracing tracing = SpringContextUtil.getBean(Tracing.class);
                Tracer tracer = tracing.tracer();
                TraceContext parent = tracing.currentTraceContext().get();
                span = tracer.startScopedSpanWithParent("defaultSpanName",
                        parent);
            } catch (Exception e) {
                //
            }
            HOContext context = HOContext.getContext();
            ScopedSpan finalSpan = span;
            return () -> {
                try {
                    if (context == null) {
                        HOContext.setContext(new HOContext());
                    } else {
                        HOContext.setContext(context);
                    }
                    return runnable.call();
                } catch (Exception e) {
                    if (finalSpan != null) {
                        finalSpan.error(e);
                    }
                    throw e;
                } finally {
                    HOContext.remove();
                    Optional.ofNullable(finalSpan).ifPresent(ScopedSpan::finish);
                }
            };
        } catch (Exception e) {
            return runnable;
        }
    }
}
