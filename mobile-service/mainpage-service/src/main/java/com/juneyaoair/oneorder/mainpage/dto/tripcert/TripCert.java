package com.juneyaoair.oneorder.mainpage.dto.tripcert;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@ApiModel
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TripCert {
    /**
     * 邮寄代码
     */
    @ApiModelProperty("邮寄代码")
    private String shipMethod;
    /**
     * 邮寄方式名称
     */
    @ApiModelProperty("邮寄方式名称")
    private String shipMethName;
    /**
     * 邮寄费用
     */
    @ApiModelProperty("邮寄费用")
    private double shipPrice;
}
