package com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf;


import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.constant.PatternCommon;
import com.juneyaoair.oneorder.cuss.TaxInfo;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.ticket.DetrSegmentDetail;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.util.BffI18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.thymeleaf.TemplateEngine;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

public abstract class AbstractThymeleafProcessor {

    @Resource
    RedisUtil redisUtil;
    @Resource
    CacheService cacheService;
    @Resource
    protected TemplateEngine templateEngine;

    /**
     * @description 准备行程单数据 根据上下文中的信息，准备和设置行程单相关的数据
     * <AUTHOR>
     * @date 2025/1/20 14:27
     **/
    private void prepareData(ItineraryContext context) {
        context.getedDataContext = new GetedDataContext();
        context.getedDataContext.template = "";
        context.getedDataContext.segmentTemplate = "";
        context.getedDataContext.fareCompute = "";
        context.getedDataContext.tax = "";
        context.getedDataContext.fare = context.flightItineraryInfo.getCurrencyType() + context.flightItineraryInfo.getFare().toString();
        context.getedDataContext.totalAmount = context.flightItineraryInfo.getCurrencyTypeTotal() + context.flightItineraryInfo.getTotalAmount().toString();
        context.getedDataContext.payMethod = "";
        context.getedDataContext.issueDate = "";
        if (context.isEnglish) {
            List<TaxInfo> taxInfoList = context.flightItineraryInfo.getTaxInfoList();
            if (CollectionUtils.isNotEmpty(taxInfoList)) {
                for (TaxInfo taxInfo : taxInfoList) {
                    context.getedDataContext.tax += taxInfo.getTaxCurrencyType() + taxInfo.getTaxAmount() + taxInfo.getTaxCode() + ",";
                }
            }
            context.getedDataContext.tax = context.getedDataContext.tax.substring(0, context.getedDataContext.tax.length() - 1);
        } else {
            String currencyTypeTotal = context.flightItineraryInfo.getCurrencyTypeTotal();
            if (StringUtils.isBlank(currencyTypeTotal) && CollectionUtils.isNotEmpty(context.flightItineraryInfo.getTaxInfoList())) {
                currencyTypeTotal = context.flightItineraryInfo.getTaxInfoList().get(0).getTaxCurrencyType();
            }
            context.getedDataContext.tax = currencyTypeTotal + context.flightItineraryInfo.getTax();
        }
        String signingInfo = Optional.ofNullable(context.flightItineraryInfo.getSigningInfo()).orElse("");
        Pattern pattern = Pattern.compile(PatternCommon.GV_GROUP);
        //对于IT票直接显示IT标记
        if (context.flightItineraryInfo.isIT() || pattern.matcher(signingInfo).find()) {
            context.getedDataContext.fare = "IT";
            context.getedDataContext.tax = "IT";
            context.getedDataContext.totalAmount = "IT";
        }
        StringBuilder certNo = new StringBuilder(context.flightItineraryInfo.getIdNo() == null ? "" : context.flightItineraryInfo.getIdNo());
        context.getedDataContext.certNo = certNo;
        prepareDiffData(context);
    }

    /**
     * @description 差异化数据处理
     * <AUTHOR>
     * @date 2025/3/13 18:03
     * @param context
     * @return void
     **/
    protected abstract void prepareDiffData(ItineraryContext context);

    public String process(ItineraryContext context) {
        prepareData(context);
        ArrayList<Segment> segments = new ArrayList<>();
        for (DetrSegmentDetail segmentInfo : context.flightItineraryInfo.getDetrSegmentDetailList()) {

            Date deptTime = DateUtil.toDate(segmentInfo.getDepTime(), DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
            Date arrTime = DateUtil.toDate(segmentInfo.getArrTime(), DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
            int days = 0;
            if (StringUtils.isNotBlank(segmentInfo.getDepTime()) && StringUtils.isNotBlank(segmentInfo.getArrTime())) {
                days = DateUtil.diffDays(segmentInfo.getDepTime().substring(0, 10),
                        segmentInfo.getArrTime().substring(0, 10),
                        DateUtil.YYYY_MM_DD_PATTERN);
            }
            //计划起飞时间
            String depTimeStr = "";
            //计划到达时间
            String arrTimeStr = "";
            //航班日期
            String flightDateStr = "";
            String deptAirport = "";
            String arrAirport = "";
            ApiAirPortInfoDto depAirportInfo = cacheService.getLocalAirport(segmentInfo.getDepAirportCode());
            ApiAirPortInfoDto airPortInfoDto = cacheService.getLocalAirport(segmentInfo.getArrAirportCode());
            if(context.isEnglish || LanguageEnum.ZH_CN != context.language){
                LanguageEnum language = context.language;
                if(context.isEnglish){
                    language = LanguageEnum.EN_US;
                }
                //英文邮件
                if (null != deptTime) {
                    depTimeStr = DateUtil.convertDateToString(deptTime, "HHmm");
                    SimpleDateFormat dateFormat = new SimpleDateFormat("ddMMMyy", Locale.UK);
                    flightDateStr = dateFormat.format(deptTime);
                }
                if (null != arrTime) {
                    arrTimeStr = DateUtil.convertDateToString(arrTime, "HHmm");
                }
                //根据语言进行名称翻译
                if (null != depAirportInfo) {
                    deptAirport = depAirportInfo.getAirPortCode() + "--" + BffI18nUtils.getAirportName(depAirportInfo,language.name()) + "," + BffI18nUtils.getCityName(depAirportInfo,language.name());
                }
                if (null != airPortInfoDto) {
                    arrAirport = airPortInfoDto.getAirPortCode() + "--" + BffI18nUtils.getAirportName(airPortInfoDto,language.name()) + "," + BffI18nUtils.getCityName(airPortInfoDto,language.name());
                }
            } else{
                if (null != deptTime) {
                    depTimeStr = DateUtil.convertDateToString(deptTime, "HH:mm");
                    flightDateStr = DateUtil.convertDateToString(deptTime, "yyyy/MM/dd");
                }
                if (null != arrTime) {
                    arrTimeStr = DateUtil.convertDateToString(arrTime, "HH:mm");
                }
                if (null != depAirportInfo) {
                    deptAirport = depAirportInfo.getCityName() + depAirportInfo.getAirPortName();
                }
                if (null != airPortInfoDto) {
                    arrAirport = airPortInfoDto.getCityName() + airPortInfoDto.getAirPortName();
                }
            }
            //计划起飞时间
            if (StringUtils.isBlank(depTimeStr)) {
                //计划起飞时间
                depTimeStr = "--";
            }
            if (StringUtils.isBlank(arrTimeStr)) {
                arrTimeStr = "--";
            }
            if (depTimeStr.equals(arrTimeStr)) {
                arrTimeStr = "--";
            }
            String segmentStatus = "--";
            if (StringUtils.isNotBlank(segmentInfo.getSegmentStatus())) {
                segmentStatus = segmentInfo.getSegmentStatus();
            }
            Segment segment = new Segment();
            segment.setFlightNo(segmentInfo.getFlightNo());
            segment.setFlightDate(flightDateStr);
            segment.setCabinCode(segmentInfo.getCabin());
            segment.setDepAirport(deptAirport);
            segment.setArrAirport(arrAirport);
            segment.setDepTime(depTimeStr);
            segment.setArrTime(arrTimeStr);
            segment.setSegmentStatus(segmentStatus);
            segment.setDepAirportTerminal(segmentInfo.getDepAirportTerminal());
            segment.setArrAirportTerminal(segmentInfo.getArrAirportTerminal());
            if (days > 0) {
                segment.setDays("+" + days);
            } else {
                segment.setDays("");
            }
            if (StringUtils.isBlank(segmentInfo.getBaggageWeightUnit())) {
                segment.setBaggageWeight(segmentInfo.getBaggagePiece() + "PC");
            } else {
                segment.setBaggageWeight(segmentInfo.getBaggageWeight() + segmentInfo.getBaggageWeightUnit());
            }
            segments.add(segment);
        }
        context.getedDataContext.segmentInfos = segments;
        return templateProcess(context);
    }

    protected abstract String templateProcess(ItineraryContext context);
}
