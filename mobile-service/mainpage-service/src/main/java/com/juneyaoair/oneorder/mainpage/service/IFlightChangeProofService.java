package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofApply;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofResp;
import com.juneyaoair.oneorder.mainpage.dto.changeproof.FlightChangeProofSendInfo;
import com.juneyaoair.oneorder.mainpage.service.impl.VerifyFlightDelayCodeReq;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import org.springframework.validation.BindingResult;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IFlightChangeProofService {

    /**
     * 申请航变证明
     *
     * @param req
     * @param request
     * @return
     */
    ResponseData<List<FlightChangeProofResp>> applyFlightChangeProof(RequestData<FlightChangeProofApply> req, HttpServletRequest request);

    ResponseData<List<String>> sendFlightChangeProof(RequestData<FlightChangeProofSendInfo> req, HttpServletRequest request, HttpServletResponse response);

    /**
     * 航班延误证明验真
     */
    ResponseData<Boolean> verifyFlightDelayCode(RequestData<VerifyFlightDelayCodeReq> req, HttpServletRequest request, BindingResult bindingResult);
}
