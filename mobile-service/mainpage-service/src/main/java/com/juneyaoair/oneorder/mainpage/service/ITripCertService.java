package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.oneorder.mainpage.dto.tripcert.*;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import org.springframework.validation.BindingResult;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface ITripCertService {
    ResponseData<SubmitTripOrderResp> submitTripOrder(RequestData<DeliveryBooking> baseReq, HttpServletRequest request, BindingResult bindingResult);

    /**
     * @description 验证是否符合开具条件
     * <AUTHOR>
     * @date 2024/12/12 14:23
     * @param baseReq
     * @param bindingResult
     * @param request
     * @return ResponseData<List<DeliveryTicketInfo>>
     **/
    ResponseData<List<DeliveryTicketInfo>> checkTicket(RequestData<DeliveryTicketInfoUser> baseReq, BindingResult bindingResult, HttpServletRequest request);
}
