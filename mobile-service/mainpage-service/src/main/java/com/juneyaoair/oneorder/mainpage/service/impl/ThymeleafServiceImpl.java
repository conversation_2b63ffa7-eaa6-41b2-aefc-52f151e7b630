package com.juneyaoair.oneorder.mainpage.service.impl;

import com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf.AbstractThymeleafProcessor;
import com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf.ItineraryContext;
import com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf.ThymeleafType;
import com.juneyaoair.oneorder.mainpage.service.IThymeleafService;
import org.springframework.stereotype.Service;

@Service
public class ThymeleafServiceImpl implements IThymeleafService {
    @Override
    public String createEnItineraryHtml(ItineraryContext context) {
        AbstractThymeleafProcessor processor = ThymeleafType.getProcessor(context.isEnglish,context.language);
        return processor.process(context);
    }
}
