package com.juneyaoair.oneorder.mainpage.service;

import com.juneyaoair.oneorder.mainpage.dto.suggestion.ExpressViewsRequest;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import org.springframework.validation.BindingResult;

import javax.servlet.http.HttpServletRequest;

public interface ISuggestionService {

    ResponseData<Boolean> expressViews(RequestData<ExpressViewsRequest> req, HttpServletRequest request, BindingResult bindingResult);
}
