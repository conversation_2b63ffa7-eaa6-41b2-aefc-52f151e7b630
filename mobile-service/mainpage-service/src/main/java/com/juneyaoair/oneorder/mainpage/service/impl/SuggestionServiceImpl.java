package com.juneyaoair.oneorder.mainpage.service.impl;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.juneyaoair.flightbasic.common.UnifiedOrderResultEnum;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.order.dto.activity.BaseResultDTO;
import com.juneyaoair.oneorder.api.order.dto.activity.PtSuggestionDto;
import com.juneyaoair.oneorder.api.order.service.impl.IActivityService;
import com.juneyaoair.oneorder.api.passager.IPassagerService;
import com.juneyaoair.oneorder.api.passager.dto.PriSugComB2CRequest;
import com.juneyaoair.oneorder.api.passager.dto.PriSugComB2CResponse;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.util.BeanUtils;
import com.juneyaoair.oneorder.mainpage.config.MainPageConfig;
import com.juneyaoair.oneorder.mainpage.constant.MainPageConstant;
import com.juneyaoair.oneorder.mainpage.constant.WSEnum;
import com.juneyaoair.oneorder.mainpage.dto.suggestion.ExpressViewsRequest;
import com.juneyaoair.oneorder.mainpage.service.ISuggestionService;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.mobile.exception.util.AESTool;
import com.juneyaoair.oneorder.tools.utils.HttpUtil;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Service
@Slf4j
public class SuggestionServiceImpl implements ISuggestionService {
    @Resource
    IPassagerService passagerService;
    @Resource
    MainPageConfig mainPageConfig;
    @Resource
    IActivityService activityService;

    @Override
    public ResponseData<Boolean> expressViews(RequestData<ExpressViewsRequest> req, HttpServletRequest request, BindingResult bindingResult) {
        ResponseData<Boolean> response = ResponseData.success();
        String ip = ServiceContext.getHead().clientIp;
        ChannelInfo channelInfo = ServiceContext.getHead().channelInfo;
        decrypt(req);
        //校验参数
        if (bindingResult.hasErrors()) {
            throw ServiceException.fail(bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        if (req.getData().getContent().length() < 50) {
            throw ServiceException.fail("内容至少输入50个字符");
        }
        if (req.getData().getContent().length() > 500) {
            throw ServiceException.fail("内容最多只能输入500个字符");
        }
        String url = req.getData().getUrl();
        if (!StringUtil.isNullOrEmpty(url)) {
            String regEx = "[,]";
            String term = url.replaceAll(regEx, "");
            int count = url.length() - term.length();
            if (count > 5) {//最多6张照片
                throw ServiceException.fail("最多只能上传6张照片");
            }
        }
        if ("P".equalsIgnoreCase(mainPageConfig.getSuggestionSystem())) {
            PriSugComB2CRequest<ExpressViewsRequest> applyRequest = new PriSugComB2CRequest<>();
            applyRequest.setInftCode("priSugComB2C");
            applyRequest.setMemberId(req.getFfpNo());
            applyRequest.setData(req.getData());

            PriSugComB2CResponse priSugComB2CResponse = passagerService.addPriSugComB2C(applyRequest);

            if (priSugComB2CResponse == null) {
                throw ServiceException.fail("信息提交失败");
            }
            if (0 == priSugComB2CResponse.getCode()) {
                response.setMessage(WSEnum.SUCCESS.getResultInfo());
            } else {
                response.setCode(WSEnum.ERROR.getResultCode());
                if (StringUtils.isNotEmpty(priSugComB2CResponse.getMsg())) {
                    response.setMessage(priSugComB2CResponse.getMsg());
                } else {
                    throw ServiceException.fail("信息提交失败");
                }
            }
        } else {
            PtSuggestionDto suggestionDto = new PtSuggestionDto();
            suggestionDto.setVersion("10");
            suggestionDto.setChannelCode(channelInfo.getChannelCode());
            suggestionDto.setUserNo(channelInfo.getUserNo());
            BeanUtils.copyNotNullProperties(req.getData(), suggestionDto);

            Map<String, String> headMap = HttpUtil.getHeaderMap(ip, "");
            BaseResultDTO feedback = activityService.createFeedback(suggestionDto, headMap);
            if (feedback == null || !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(feedback.getResultCode())) {
                throw ServiceException.fail("信息提交失败");
            }
        }
        return response;
    }

    private void decrypt(RequestData<ExpressViewsRequest> req) {
        ExpressViewsRequest data = req.getData();
        if (StrUtil.isNotBlank(data.getPassContact())) {
            try {
                data.setPassContact(AESTool.decrypt(data.getPassContact(),
                        CrmConfig.DEFAULT_TOKEN.substring(0, 16),
                        CrmConfig.DEFAULT_TOKEN.substring(0, 16)));
            } catch (Exception e){
                log.error("AESTool decrypt PassContact fail");
                throw new ServiceException("系统繁忙");
            }
        }
        if (!ReUtil.isMatch("[0-9-_,]+",data.getPassContact())) {
            throw ServiceException.fail("联系方式仅支持数字及标点");
        }
        if (StrUtil.isNotBlank(data.getPassIdCard())) {
            try {
                data.setPassIdCard(AESTool.decrypt(data.getPassIdCard(),
                        CrmConfig.DEFAULT_TOKEN.substring(0, 16),
                        CrmConfig.DEFAULT_TOKEN.substring(0, 16)));
            } catch (Exception e){
                log.error("AESTool decrypt PassIdCard fail");
                throw new ServiceException("系统繁忙");
            }
        }else {
            throw ServiceException.fail("旅客证件号不能为空");
        }
        if (StrUtil.isNotBlank(data.getPassName())) {
            try {
                data.setPassName(AESTool.decrypt(data.getPassName(),
                        CrmConfig.DEFAULT_TOKEN.substring(0, 16),
                        CrmConfig.DEFAULT_TOKEN.substring(0, 16)));
            } catch (Exception e){
                log.error("AESTool decrypt PassName fail");
                throw new ServiceException("系统繁忙");
            }
        }
        if (!ReUtil.isMatch(MainPageConstant.CN_EN_REG_EXP +"{1,10}",data.getPassName())) {
            throw ServiceException.fail("旅客姓名只支持中英文，且最多输入10个字符");
        }
    }
}
