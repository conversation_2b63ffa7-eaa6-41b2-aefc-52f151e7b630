package com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf;

import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.ticket.FlightItineraryInfo;
import com.juneyaoair.oneorder.ticket.TicketInfoQueryResponse;

import java.util.List;

public class ItineraryContext {
    public boolean isEnglish;//requirement
    public FlightItineraryInfo flightItineraryInfo;//requirement
    public List<TicketInfoQueryResponse> data;//requirement
    public GetedDataContext getedDataContext;
    public LanguageEnum language;
    public String hoLogo;
    public String iataLogo;
}
