package com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf;

import com.alibaba.fastjson2.JSONArray;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.util.SpringContextUtil;

/**
 * Itinerary的Thymeleaf模板枚举
 */
public enum ThymeleafType {
    CHINESE( new JSONArray(LanguageEnum.ZH_CN), ItineraryThymeleafChineseProcessor.class),
    English(new JSONArray(LanguageEnum.EN_US), ItineraryThymeleafEngProcessor.class),
    JA_JP(new JSONArray(LanguageEnum.JA_JP), ItineraryThymeleafJpProcessor.class),
    ZH_HK( new JSONArray(LanguageEnum.ZH_HK), ItineraryThymeleafHkProcessor.class)
    ;


    ThymeleafType(JSONArray languageArr, Class<? extends AbstractThymeleafProcessor> _clazz) {
        this.languageArr = languageArr;
        this._clazz = _clazz;

    }

    public static AbstractThymeleafProcessor getProcessor(boolean isEnglish,LanguageEnum language) {
        ThymeleafType type = English;
        for (ThymeleafType thymeleafType : values()) {
            if (thymeleafType.languageArr.contains(language) ) {
                type = thymeleafType;
                break;
            }
        }
        //中文可以自由选择发送语言版本
        if(LanguageEnum.ZH_CN.equals(language)){
            type = isEnglish ? English : CHINESE;
        }
        return SpringContextUtil.getBean(type._clazz);
    }
    /**
     * 语言清单 来源 LanguageEnum
     */
    private final JSONArray languageArr;
    private Class<? extends AbstractThymeleafProcessor> _clazz;
}
