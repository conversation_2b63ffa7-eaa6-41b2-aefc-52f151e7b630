package com.juneyaoair.oneorder.mainpage.controller;

import com.juneyaoair.flightbasic.request.notice.NoticeRequestDTO;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.mainpage.service.ProvisionServiceAggr;
import com.juneyaoair.oneorder.policy.NoticeText;
import com.juneyaoair.oneorder.policy.PolicyModule;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/30 9:41
 */
@Slf4j
@Api(value = "ProvisionController", tags = "条款服务管理")
@RequestMapping("/provision")
@RestController
public class ProvisionController extends BaseController {
    @Autowired
    private ProvisionServiceAggr provisionServiceAggr;
    @ApiOperation(value = "获取服务条款列表", notes = "获取服务条款列表")
    @PostMapping("/queryProvisionList")
    public ResponseData<List<PolicyModule>> queryProvisionList(@RequestBody @Validated RequestDataDto<NoticeRequestDTO> requestData, BindingResult bindingResult, HttpServletRequest request) {
        checkParam(requestData,bindingResult);
        BizDto bizDto = initBizDto(request);
        return ResponseData.suc(provisionServiceAggr.getAllNoticeInfo(bizDto, requestData.getLanguage(), requestData.getData()));
    }


    @ApiOperation(value = "queryProvisionDetail", notes = "获取服务条款富文本详情")
    @PostMapping("/queryProvisionDetail")
    public ResponseData<NoticeText> queryProvisionDetail(@RequestBody @Validated RequestDataDto<NoticeRequestDTO> requestData, BindingResult bindingResult, HttpServletRequest request) {
        checkParam(requestData,bindingResult);
        BizDto bizDto = initBizDto(request);
        return ResponseData.suc(provisionServiceAggr.getRichTextNoticeInfo(bizDto,requestData.getData()));
    }
}
