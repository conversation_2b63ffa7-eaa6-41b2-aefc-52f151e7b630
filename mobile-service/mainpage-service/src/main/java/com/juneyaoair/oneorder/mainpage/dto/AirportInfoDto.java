package com.juneyaoair.oneorder.mainpage.dto;

import com.juneyaoair.flightbasic.response.airport.AirPortExtraInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/21 13:51
 */
@Data
@ApiModel(value = "AirportInfoDto",description ="机场信息")
public class AirportInfoDto {
    @ApiModelProperty(value = "机场三字码")
    private String airportCode;
    @ApiModelProperty(value = "机场名称")
    private String airportName;
    @ApiModelProperty(value = "机场英文名")
    private String airportEName;
    @ApiModelProperty(value = "机场名称")
    private Map<String, String> airportNameMap;
    @ApiModelProperty(value = "城市三字码")
    private String cityCode;
    @ApiModelProperty(value = "城市名称")
    private String cityName;
    @ApiModelProperty(value = "城市名称")
    private Map<String, String> cityNameMap;
    @ApiModelProperty(value = "城市拼音")
    private String cityPinYin;
    @ApiModelProperty(value = "机场拼音")
    private String airportPinyin;
    @ApiModelProperty(value = "公务舱休息室位置")
    private String viproom;
    @ApiModelProperty(value = "航站楼位置")
    private String terminalposition;
    @ApiModelProperty(value = "登机口信息")
    private String gateCloseDate;
    @ApiModelProperty(value = "值机柜台信息")
    private String checkincounter;
    @ApiModelProperty(value = "公务舱/金卡/白金卡值机柜台")
    private String firstclasscheckincounter;
    @ApiModelProperty(value = "机场售票柜台信息")
    private String ticketcounter;
    @ApiModelProperty(value = "国际-航站楼位置")
    private String iTerminalposition;
    @ApiModelProperty(value = "国际-值机柜台信息")
    private String iCheckincounter;
    @ApiModelProperty(value = "国际-公务舱/金卡/白金卡值机柜台")
    private String iFirstclasscheckincounter;
    @ApiModelProperty(value = "国际-公务舱休息室位置")
    private String iViproom;
    @ApiModelProperty(value = "国际-机场售票柜台信息")
    private String iTicketcounter;
    @ApiModelProperty(value = "值机开放时间")
    private String checkinbegintime;
    @ApiModelProperty(value = "值机关闭时间")
    private String checkinendtime;
    @ApiModelProperty("机场额外信息 key:机场 value:额外信息")
    private Map<String, AirPortExtraInfo> airportExtraInfoMap;
}
