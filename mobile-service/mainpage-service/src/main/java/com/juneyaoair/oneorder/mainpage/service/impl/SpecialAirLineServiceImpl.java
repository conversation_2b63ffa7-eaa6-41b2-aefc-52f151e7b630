package com.juneyaoair.oneorder.mainpage.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.juneyaoair.flightbasic.common.BaseReq;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.WSEnum;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.feign.FlightBasicProviderClient;
import com.juneyaoair.flightbasic.request.wechat.SpecialAirlineDTO;
import com.juneyaoair.flightbasic.response.api.ApiCityInfoDto;
import com.juneyaoair.flightbasic.response.city.CityInfoDTO;
import com.juneyaoair.mobile.exception.util.HoAirIpUtil;
import com.juneyaoair.oneorder.api.basic.service.IBasicService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.fare.dto.display.QueryLowPriceCalReqDTO;
import com.juneyaoair.oneorder.fare.dto.display.QueryLowPriceCalResDTO;
import com.juneyaoair.oneorder.fare.dto.display.QueryLowPriceDTO;
import com.juneyaoair.oneorder.fare.feign.FareClient;
import com.juneyaoair.oneorder.mainpage.dto.SpecialAirLine;
import com.juneyaoair.oneorder.mainpage.mapstruct.SpecialAirlineDTOMapstruct;
import com.juneyaoair.oneorder.mainpage.service.ISpecialAirLineService;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.util.BffI18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/8 16:03
 */
@Service
public class SpecialAirLineServiceImpl extends CommonService implements ISpecialAirLineService {
    @Resource
    private FlightBasicConsumerClient flightBasicConsumerClient;
    @Autowired
    private FlightBasicProviderClient flightBasicProviderClient;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RedisConstantConfig redisConstantConfig;
    @Resource
    private FareClient fareClient;
    @Resource
    private IBasicService basicService;
    @Resource
    private CacheService cacheService;

    @Override
    public List<SpecialAirLine> querySpecialAirlinePrice(BizDto bizDto, String currency, @NotNull LanguageEnum language) {
        BaseReq<Object> baseReq = createBaseReq(bizDto, null);
        BaseResultDTO<List<SpecialAirlineDTO>> baseResponseDTO = flightBasicProviderClient.getSpecialAirLinesV2(baseReq);
        if (WSEnum.SUCCESS.resultCode.equals(baseResponseDTO.getResultCode()) && CollectionUtils.isNotEmpty(baseResponseDTO.getResult())) {
            List<SpecialAirlineDTO> allSpecialAirlineDTOList = baseResponseDTO.getResult();
            //获取低价数据
            fetchLowPriceData(allSpecialAirlineDTOList, currency);
            List<SpecialAirLine> specialAirlineDTOList = SpecialAirlineDTOMapstruct.mapper.toSpecialAirLineList(allSpecialAirlineDTOList);

            // 处理城市名称翻译
            if (CollectionUtils.isNotEmpty(specialAirlineDTOList)) {
                for (SpecialAirLine specialAirLine : specialAirlineDTOList) {
                    // 出发城市翻译
                    if (StringUtils.isNotBlank(specialAirLine.getDepCityCode())) {
                        ApiCityInfoDto depCityInfo = cacheService.getLocalCity(specialAirLine.getDepCityCode());
                        if (depCityInfo != null) {
                            specialAirLine.setDepCity(BffI18nUtils.getCityName(depCityInfo, language));
                        }
                    }

                    // 到达城市翻译
                    if (StringUtils.isNotBlank(specialAirLine.getArrCityCode())) {
                        ApiCityInfoDto arrCityInfo = cacheService.getLocalCity(specialAirLine.getArrCityCode());
                        if (arrCityInfo != null) {
                            specialAirLine.setArrCity(BffI18nUtils.getCityName(arrCityInfo, language));
                        }
                    }
                }
            }

            return specialAirlineDTOList;
        }
        return new ArrayList<>();
    }

    /**
     * 抓取低价日历数据
     *
     * @param specialAirlineDTOList
     */
    private void fetchLowPriceData(List<SpecialAirlineDTO> specialAirlineDTOList, String currency) {
        if (CollUtil.isEmpty(specialAirlineDTOList)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(specialAirlineDTOList)) {
            for (SpecialAirlineDTO specialAirlineDTO : specialAirlineDTOList) {
                RequestData<QueryLowPriceCalReqDTO> requestData = new RequestData<>();
                requestData.setFfpId(ServiceContext.getHead().ffpId);
                requestData.setFfpNo(ServiceContext.getHead().ffpNo);
                requestData.setOriginIp(ServiceContext.getHead().clientIp);
                requestData.setChannelNo(ServiceContext.getHead().channelNo);
                requestData.setCurrency(StringUtils.isBlank(currency) ? "CNY" : currency);
                QueryLowPriceCalReqDTO reqDTO = new QueryLowPriceCalReqDTO();
                Date curDate = new Date();
                requestData.setData(reqDTO);
                reqDTO.setStartFlightDate(DateUtil.dateToString(curDate, DateUtil.YYYY_MM_DD_PATTERN));
                //30 days
                reqDTO.setEndFlightDate(DateUtil.dateToString(DateUtil.addOrLessDay(curDate, 30), DateUtil.YYYY_MM_DD_PATTERN));
                reqDTO.setDepCity(specialAirlineDTO.getDepCityCode());
                reqDTO.setArrCity(specialAirlineDTO.getArrCityCode());
                reqDTO.setRouteType("OW");//单程
                ResponseData<QueryLowPriceCalResDTO> responseData = fareClient.queryLowPriceInfoI18n(requestData);
                if (responseData == null || responseData.getData() == null || CollUtil.isEmpty(responseData.getData().getQueryLowPriceList())) {
                    continue;
                }
                QueryLowPriceDTO queryLowPriceDTO = responseData.getData().getQueryLowPriceList().stream()
                        .filter(i -> i != null && i.getLowPriceValue() != null)
                        .min(Comparator.comparing(QueryLowPriceDTO::getLowPriceValue)).orElse(null);
                if (queryLowPriceDTO == null) {
                    continue;
                }
                specialAirlineDTO.setPrice(queryLowPriceDTO.getLowPriceValue().intValue());
                specialAirlineDTO.setActiveTime(queryLowPriceDTO.getFlightDate());
            }
            //剔除没有价格展示的
            specialAirlineDTOList.removeIf(specialAirlineDTO -> specialAirlineDTO.getPrice() <= 0D);
        }
    }

    /**
     * 赋值特价航线低价价格与日期
     *
     * @param specialAirlineDTO
     * @param map
     */
    private void obtainLowDate(SpecialAirlineDTO specialAirlineDTO, Map<String, Double> map) {
        if (map.size() > 0) {
            map.entrySet().removeIf(entry -> Objects.isNull(entry.getValue()));
            Double value = null;
            Optional<Double> optionalDouble = map.values().stream().sorted().findFirst();
            if (optionalDouble.isPresent()) {
                value = optionalDouble.get();
            }
            Double finalValue = value;
            List<String> collect = map.entrySet()
                    .stream()
                    .filter(kvEntry -> Objects.equals(kvEntry.getValue(), finalValue))
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            //若值有重复的，则collect集合中有多个key，按照业务需求自行取值
            specialAirlineDTO.setPrice(value);
            specialAirlineDTO.setActiveTime(collect.get(0));
        }
    }
}
