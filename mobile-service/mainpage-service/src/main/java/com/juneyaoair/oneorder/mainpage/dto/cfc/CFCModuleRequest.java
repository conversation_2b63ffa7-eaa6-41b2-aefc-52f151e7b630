package com.juneyaoair.oneorder.mainpage.dto.cfc;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CFCModuleRequest {

    @NotBlank(message = "出发机场 不能为空")
    @ApiModelProperty(value = "出发机场", required = true)
    private String depCode;

    @NotBlank(message = "到达机场 不能为空")
    @ApiModelProperty(value = "到达机场", required = true)
    private String arrCode;

    @NotBlank(message = "航班类型 不能为空")
    @Pattern(regexp = "[OW|RT]{2}", message = "航班类型只能为单程：OW；往返：RT！")
    @ApiModelProperty(value = "航班类型:单程、往返", allowableValues = "OW/RT", required = true)
    private String flightType;

    @NotBlank(message = "出发日期 不能为空")
    @Pattern(regexp = "(20[0-3][0-9]-(0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|(20[0-3][0-9]-(0[2469]|11)-(0[1-9]|[12][0-9]|30))", message = "日期格式错误，应为YYYY-MM-DD")
    @ApiModelProperty(value = "出发日期", allowableValues = "yyyy-MM-dd", required = true)
    private String depDate;
}
