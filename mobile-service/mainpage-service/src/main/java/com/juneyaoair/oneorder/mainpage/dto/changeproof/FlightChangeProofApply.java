package com.juneyaoair.oneorder.mainpage.dto.changeproof;

import com.juneyaoair.oneorder.common.dto.GeetestDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;


@ApiModel
@NoArgsConstructor
@Data
public class FlightChangeProofApply extends GeetestDto {
    /**
     * 旅客证件号/票号
     */
    @ApiModelProperty("旅客证件号/票号")
    @NotBlank(message = "证件号或票号不能为空")
    private String passengerCard;

    @NotBlank(message = "旅客姓名不可为空")
    @ApiModelProperty(value = "旅客姓名")
    private String passengerName;
    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    @NotBlank(message = "航班日期不能为空")
    private String flightDate;
    /**
     * MD5(MD5(passengerCard+flightDate+channelCode+flightNo+secret))
     */
    @ApiModelProperty("MD5(MD5(passengerCard+flightDate+channelCode+flightNo+secret))")
    private String key;
    @ApiModelProperty("航班号")
    private String flightNo;

}
