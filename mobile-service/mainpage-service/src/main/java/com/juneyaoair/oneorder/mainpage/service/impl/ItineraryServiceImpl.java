package com.juneyaoair.oneorder.mainpage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.juneyaoair.dsop.sdk.util.MD5Util;
import com.juneyaoair.flightbasic.appenum.MemberDetailRequestItemsEnum;
import com.juneyaoair.flightbasic.common.BaseRequestDTO;
import com.juneyaoair.flightbasic.common.BaseResultDTO;
import com.juneyaoair.flightbasic.common.UnifiedOrderResultEnum;
import com.juneyaoair.flightbasic.feign.FlightBasicConsumerClient;
import com.juneyaoair.flightbasic.response.api.ApiAirPortInfoDto;
import com.juneyaoair.i18n.LocaleUtil;
import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.mobile.exception.util.AESTool;
import com.juneyaoair.oneorder.api.common.CacheService;
import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.config.CrmConfig;
import com.juneyaoair.oneorder.api.crm.service.IMemberService;
import com.juneyaoair.oneorder.api.crm.utils.CRMReqUtil;
import com.juneyaoair.oneorder.api.cuss.dto.DetrMemberInfo;
import com.juneyaoair.oneorder.api.cuss.dto.TicketVerifyResponse;
import com.juneyaoair.oneorder.api.cuss.service.IVerifyTicketService;
import com.juneyaoair.oneorder.api.dsop.BigDataConfig;
import com.juneyaoair.oneorder.api.dsop.service.IDsopService;
import com.juneyaoair.oneorder.api.email.EmailConfigEnum;
import com.juneyaoair.oneorder.api.email.dto.SendEmailCodeParam;
import com.juneyaoair.oneorder.api.email.service.IMailService;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.DigestmodEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.api.order.service.IOrderService;
import com.juneyaoair.oneorder.common.ServiceContext;
import com.juneyaoair.oneorder.common.concurrency.ThreadPool;
import com.juneyaoair.oneorder.common.constant.ContactTypeEnum;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.CheckDayLicense;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.CaptchaFuncEnum;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.CheckLicenseFuncEnum;
import com.juneyaoair.oneorder.common.dto.enums.EticketStatusEnum;
import com.juneyaoair.oneorder.common.enums.LanguageEnum;
import com.juneyaoair.oneorder.common.service.SmsCodeSendService;
import com.juneyaoair.oneorder.common.service.TongDunService;
import com.juneyaoair.oneorder.config.LimitTimesConfig;
import com.juneyaoair.oneorder.config.TongDunConfig;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.crm.dto.PtCRMResponse;
import com.juneyaoair.oneorder.crm.dto.common.MemberContactSoaModel;
import com.juneyaoair.oneorder.crm.dto.request.PtMemberDetailRequest;
import com.juneyaoair.oneorder.crm.dto.response.PtMemberDetail;
import com.juneyaoair.oneorder.mainpage.config.EmailConfig;
import com.juneyaoair.oneorder.mainpage.config.MainPageConfig;
import com.juneyaoair.oneorder.mainpage.constant.WSEnum;
import com.juneyaoair.oneorder.mainpage.dto.sendtravelitinerary.TicketVerifyEmailRequest;
import com.juneyaoair.oneorder.mainpage.dto.travelitinerary.PdfTravelItineraryUrlRequest;
import com.juneyaoair.oneorder.mainpage.dto.verify.FlightVerifyReq;
import com.juneyaoair.oneorder.mainpage.processor.itinerarythymeleaf.ItineraryContext;
import com.juneyaoair.oneorder.mainpage.service.IItineraryService;
import com.juneyaoair.oneorder.mainpage.service.IThymeleafService;
import com.juneyaoair.oneorder.mainpage.util.FavFTPUtil;
import com.juneyaoair.oneorder.mainpage.util.PdfUtil;
import com.juneyaoair.oneorder.mobile.RedisKeyFormatUtil;
import com.juneyaoair.oneorder.mobile.config.RedisConstantConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.mobile.dto.EmailContent;
import com.juneyaoair.oneorder.mobile.utils.RedisUtil;
import com.juneyaoair.oneorder.order.constant.TicketStateEnum;
import com.juneyaoair.oneorder.order.util.JsonUtil;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.ticket.DetrSegmentDetail;
import com.juneyaoair.oneorder.ticket.FlightItineraryInfo;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import com.juneyaoair.oneorder.util.BffI18nUtils;
import com.juneyaoair.oneorder.util.MetricLogUtils;
import com.juneyaoair.oneorder.utils.SensitiveInfoHider;
import com.lowagie.text.DocumentException;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.EmailAttachment;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

import javax.annotation.Resource;
import javax.mail.internet.MimeUtility;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class ItineraryServiceImpl implements IItineraryService {

    @Resource
    RedisUtil redisUtil;
    @Resource
    private IMemberService memberService;
    @Resource
    LimitTimesConfig limitTimesConfig;
    @Resource
    private MainPageConfig mainPageConfig;
    @Autowired
    private BigDataConfig bigDataConfig;
    @Resource
    FlightBasicConsumerClient flightBasicConsumerClient;
    @Resource
    CommonService commonService;
    @Resource
    TongDunConfig tongDunConfig;
    @Resource
    ThreadPool threadPool;
    @Resource
    CacheService cacheService;
    @Resource
    IDsopService dsopService;
    @Resource
    IOrderService orderService;
    @Resource
    IThymeleafService iThymeleafService;
    @Resource
    IVerifyTicketService verifyTicketService;
    @Resource
    EmailConfig emailConfig;
    @Resource
    private IGeetestService geetestService;
    @Resource
    private SmsCodeSendService smsCodeSendService;
    @Autowired
    private IMailService mailService;
    @Autowired
    private TongDunService tongDunService;
    @Resource
    private LocaleUtil localeUtil;

    @Override
    public ResponseData<FlightItineraryInfo> ticketVerify(RequestDataDto<FlightVerifyReq> requestData, BindingResult bindingResult, BizDto bizDto) {
        String clientIp = ServiceContext.getHead().clientIp;
        String channelCode = ServiceContext.getHead().channelNo;
        HashMap<String, String> param = new HashMap<>();
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        param.put("digestmod", digestmodEnum.getName());
        param.put("user_id", bizDto.getIp()); //网站用户id
        param.put("client_type", requestData.getClient_type()); //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("ip_address", bizDto.getIp()); //传输用户请求验证时所携带的IP
        geetestService.validate(requestData.getData().getScene(), requestData, param);
        MetricLogUtils.saveMetricLog("客票验真V1-客票提取", requestData);
        // 限制IP访问次数
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.ITINERARY_IP, "查询失败，达到单日查询上限");
        // 登录情况下限制卡号访问次数
        if (StringUtils.isNotBlank(requestData.getFfpNo())) {
            CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(requestData.getFfpNo(), CheckLicenseFuncEnum.ITINERARY_FFP, "查询失败，达到账户单日操作上限");
            commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        } else {
            commonService.checkDayLicense(true, ipCheckDayLicense);
        }
        ChannelInfo channelInfo = commonService.findChannelInfo(channelCode);
        getAntifraudTask(requestData, requestData.getData().getCertNo(), channelInfo, bizDto);
        BaseResultDTO<TicketVerifyResponse> invokeResultDTO = getVerifyTicket(requestData.getData(), clientIp, channelInfo, true);
        if (ObjectUtils.isEmpty(invokeResultDTO) || ObjectUtils.isEmpty(invokeResultDTO.getResult()) || !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(invokeResultDTO.getResultCode())) {
            throw MultiLangServiceException.fail(CommonErrorCode.SEAR_TRAVELLER_NO_TRIP);
        }
        FlightItineraryInfo flightItineraryInfo = invokeResultDTO.getResult().getData();
        flightItineraryInfoBeanOpt(flightItineraryInfo, requestData.getLanguage());
        if (CollUtil.isEmpty(flightItineraryInfo.detrSegmentDetailList)) {
            throw MultiLangServiceException.fail(CommonErrorCode.SEAR_TRAVELLER_NO_TRIP);
        }
        boolean segmentMatchFlag = flightItineraryInfo.detrSegmentDetailList.stream().anyMatch(
                detrSegmentDetail -> requestData.getData().getDepCityCode().equalsIgnoreCase(detrSegmentDetail.getDepCityCode())
                        && requestData.getData().getArrCityCode().equalsIgnoreCase(detrSegmentDetail.getArrCityCode())
                        && requestData.getData().getFlightNo().equalsIgnoreCase(detrSegmentDetail.getFlightNo())
                        && requestData.getData().getFlightDate().equalsIgnoreCase(detrSegmentDetail.getDepTime().substring(0, 10))
        );
        if (!segmentMatchFlag) {
            throw MultiLangServiceException.fail(CommonErrorCode.SEAR_TRAVELLER_NO_TRIP);
        }
        //数据校验通过标记
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.createTicketVeryInfo(RedisConstantConfig.TICKET_VERY_DIR, flightItineraryInfo.getTicketNo()));
        redisUtil.set(redisKey, JsonUtil.objectToJson(flightItineraryInfo), 60 * 10L);
        //数据脱敏处理
        String name;
        if (flightItineraryInfo.getPassengerName().length() < 3) {
            name = idMask(flightItineraryInfo.getPassengerName(), 1, 0);
        } else if (flightItineraryInfo.getPassengerName().length() == 3) {
            name = idMask(flightItineraryInfo.getPassengerName(), 1, 1);
        } else {
            name = idMask(flightItineraryInfo.getPassengerName().replace("/", ""), 2, 1);
        }
        flightItineraryInfo.setPassengerName(name);
        flightItineraryInfo.setIdNo(SensitiveInfoHider.hideAllCertNo(flightItineraryInfo.getIdNo()));
        ResponseData<FlightItineraryInfo> response = ResponseData.success();
        response.setData(flightItineraryInfo);
        return response;
    }

    private BaseResultDTO<TicketVerifyResponse> getVerifyTicket(FlightVerifyReq paramDetrInfo, String clientIp, ChannelInfo channelInfo, boolean analyzeTicket) {
        DetrMemberInfo detrMemInfo = new DetrMemberInfo();
        detrMemInfo.setPassengerNm(paramDetrInfo.getPassengerNm());
        detrMemInfo.setCertNo(paramDetrInfo.getCertNo());
        detrMemInfo.setChannelNo(channelInfo.getHeadChannelCode());
        detrMemInfo.setAnalyzeTicket(analyzeTicket);
        BaseRequestDTO<DetrMemberInfo> reqDTO = new BaseRequestDTO<>();
        reqDTO.setFfpId(ServiceContext.getHead().ffpId);
        reqDTO.setFfpCardNo(ServiceContext.getHead().ffpNo);
        reqDTO.setIp(clientIp);
        reqDTO.setChannelCode(channelInfo.getHeadChannelCode());
        reqDTO.setVersion(mainPageConfig.getNEW_CHECKIN_SEAT_VERSION());
        reqDTO.setRequest(detrMemInfo);
        return verifyTicketService.verifyTicket(reqDTO);
    }

    private void getAntifraudTask(RequestDataDto requestDataDto, String tktNo, ChannelInfo channelInfo, BizDto bizDto) {
        //2021-02-07 金卡及以上会员不调用同盾风控
        String mobile = getMobile(requestDataDto, bizDto.getIp(), channelInfo);
        //同盾风控控制 https://www.kdocs.cn/l/csz5ugfKPiLo
        tongDunService.ticketVerify(bizDto, requestDataDto.getBlackBox(), requestDataDto.getFfpId(), requestDataDto.getFfpNo(), mobile, tktNo);
    }

    @Nullable
    private String getMobile(RequestDataDto<FlightVerifyReq> paramDetrInfo, String clientIp, ChannelInfo channelInfo) {
        if (StringUtils.isNotBlank(paramDetrInfo.getFfpId()) && StringUtils.isNotBlank(paramDetrInfo.getFfpNo())) {
            PtMemberDetailRequest ptMemberDetailRequest = new PtMemberDetailRequest();
            ptMemberDetailRequest.CardNO = paramDetrInfo.getFfpNo();
            ptMemberDetailRequest.RequestItems = new String[]{MemberDetailRequestItemsEnum.BASICINFO.eName, MemberDetailRequestItemsEnum.CONTACTINFO.eName,
                    MemberDetailRequestItemsEnum.STATEINFO.eName, MemberDetailRequestItemsEnum.REALVERIFYINFOS.eName,
                    MemberDetailRequestItemsEnum.CERTIFICATEINFO.eName};
            PtApiCRMRequest<PtMemberDetailRequest> ptApiCRMRequest = CRMReqUtil.buildCommReqNoToken(clientIp,
                    "-1",
                    channelInfo.getChannelCode(),
                    ptMemberDetailRequest,
                    channelInfo.getChannelPwd());

            PtCRMResponse<PtMemberDetail> ptCRMResponse = memberService.memberDetail(ptApiCRMRequest, false);
            if (ptCRMResponse != null && ptCRMResponse.getCode() == 0) {
                PtMemberDetail detail = ptCRMResponse.getData();
                //获取手机号
                return filterContactInfo(detail.getContactInfo(), ContactTypeEnum.MOBILE.getCode());
            }
        }
        return null;
    }

    @Override
    public ResponseData<String> sendTravelItinerary(RequestDataDto<TicketVerifyEmailRequest> req, BizDto bizDto, HttpServletRequest httpReq) {
        decrypt(req);
        TicketVerifyEmailRequest ticketVerifyEmailRequest = req.getData();
        ResponseData<String> resp = ResponseData.success();
        String ip = ServiceContext.getHead().clientIp;
        boolean english = "1".equals(req.getData().getType());
        MetricLogUtils.saveMetricLog("行程确认单发送-客票提取", req);
        //因为国际网站使用邮箱校验验证码，此处调整为验证邮箱
        if (ChannelCodeEnum.G_B2C.getChannelCode().equalsIgnoreCase(bizDto.getHeadChannelCode())) {
            //邮件验证码
            String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createEmailCaptchaKey(ticketVerifyEmailRequest.getEmail(), CaptchaFuncEnum.TRAVEL_CONFIRMATION_FORM.name()));
            smsCodeSendService.checkVeryCode(redisKey, ticketVerifyEmailRequest.getVeriCode());
        } else {
            //校验验证码
            smsCodeSendService.checkSmsCommonCodeByFfp(req.getFfpNo(), ticketVerifyEmailRequest.getVeriCode(), req.getChannelNo(), bizDto, false);
        }
        //获取缓存数据
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisConstantConfig.createTicketVeryInfo(RedisConstantConfig.TICKET_VERY_DIR, ticketVerifyEmailRequest.getTicketNo()));
        String info = redisUtil.getStr(redisKey);
        if (StringUtils.isBlank(info)) {
            throw MultiLangServiceException.fail(CommonErrorCode.OPERATIONAL_TIMEOUT);
        }
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(ip, CheckLicenseFuncEnum.ITINERARY_SEND_IP, "发送失败，达到单日发送上限");
        if (StringUtils.isNotBlank(req.getFfpNo())) {
            CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(req.getFfpNo(), CheckLicenseFuncEnum.ITINERARY_SEND_FFP, "发送失败，达到账户单日操作上限");
            commonService.checkDayLicense(true, ipCheckDayLicense, ffpCheckDayLicense);
        } else {
            commonService.checkDayLicense(true, ipCheckDayLicense);
        }
        FlightItineraryInfo flightItineraryInfo = JsonUtil.fromJson(info, FlightItineraryInfo.class);
        String html = getHtml(req.getData().getPassengerName(), english, flightItineraryInfo, req.getLanguage());
        String directory = mainPageConfig.getPDF_PATH();
        File file = new File(directory);
        if (!file.exists()) {
            file.mkdirs();
        }
        String fileName = flightItineraryInfo.getIssueDate() + flightItineraryInfo.getTicketNo() + flightItineraryInfo.getOrgCity() + ".pdf";
        String filePath = directory + File.separator + fileName;
        try {
            PdfUtil.createPdf(html, filePath);
        } catch (IOException e) {
            log.error("IOException异常:", e);
            throw MultiLangServiceException.fail("输出文件异常");
        } catch (DocumentException e) {
            log.error("DocumentException异常:", e);
            throw MultiLangServiceException.fail("创建文件异常");
        }
        EmailAttachment ment = new EmailAttachment();
        ment.setPath(filePath);
        String name = localeUtil.getTips("ITINERARY_NAME");
        if(StringUtils.isBlank(name)){
            name = "JUNEYAOAIR-《ITINERARY》";
        }
        try {
            ment.setName(MimeUtility.encodeText(name + ".pdf"));
        } catch (UnsupportedEncodingException e) {
            ment.setName(name);
        }
        mailService.sendEmailByCommonEmails(req.getData().getEmail(), name, html, Collections.singletonList(ment), EmailConfigEnum.BOOK);
        file = new File(filePath);
        boolean delete = file.delete();
        log.info("文件删除：" + (delete ? "Y" : "N"));
        resp.setMessage(WSEnum.SUCCESS.getResultInfo());
        return resp;
    }

    private void decrypt(RequestDataDto<TicketVerifyEmailRequest> req) {
        TicketVerifyEmailRequest data = req.getData();
        if (StrUtil.isNotBlank(data.getMobileNo())) {
            try {
                data.setMobileNo(AESTool.decrypt(data.getMobileNo(),
                        CrmConfig.DEFAULT_TOKEN.substring(0, 16),
                        CrmConfig.DEFAULT_TOKEN.substring(0, 16)));
            } catch (Exception e) {
                log.error("AESTool decrypt mobileNo fail");
                throw new ServiceException("系统繁忙");
            }
        }
    }

    private String getHtml(String passengerName, boolean english, FlightItineraryInfo flightItineraryInfo, LanguageEnum language) {
        String patternStr = passengerName.toUpperCase() + "(\\s*(CHD)?\\s*(\\(CHILD\\))?\\s*|\\s*(MR)?\\s*|\\s*(MS)?\\s*|\\s*(INF\\(\\w{3}\\d{2}\\))?\\s*(\\(INFANT\\))?\\s*)";//正则表达式
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(flightItineraryInfo.getPassengerName());
        if (!matcher.matches()) {
            throw MultiLangServiceException.fail("未查询到可开具电子客票行程单数据");
        }
        flightItineraryInfo.getDetrSegmentDetailList().forEach(segmentInfo -> {
            Date now = new Date();
            if (TicketStateEnum.USED_FLOWN.equals(segmentInfo.getTicketStatus())) {
                Date depDate = DateUtil.toDate(segmentInfo.getDepTime(), DateUtil.YYYY_MM_DD_HH_MM_PATTERN);
                if (DateUtil.durDays(depDate, now) > 27) {
                    throw MultiLangServiceException.fail("很抱歉，您当前的日期不满足可下载的条件");
                }
            } else if (TicketStateEnum.OPEN_FOR_USE.equals(segmentInfo.getTicketStatus())
                    || TicketStateEnum.CHECKED_IN.equals(segmentInfo.getTicketStatus())) {
                Date bookingDate = DateUtil.toDate(flightItineraryInfo.getIssueDate());
                if (DateUtil.durDays(bookingDate, now) > 365) {
                    throw MultiLangServiceException.fail("很抱歉，您当前的日期不满足可下载的条件");
                }
            } else if (TicketStateEnum.REFUNDED.equals(segmentInfo.getTicketStatus())) {
                throw MultiLangServiceException.fail("很抱歉，您当前的日期不满足可下载的条件");
            }
        });
        return genTravelItineraryHTML(flightItineraryInfo, english, language);
    }

    @Override
    public ResponseData<String> pdfTravelItineraryUrl(RequestDataDto<PdfTravelItineraryUrlRequest> req, BizDto bizDto, HttpServletResponse response) {
        String clientIp = ServiceContext.getHead().clientIp;
        String channelCode = ServiceContext.getHead().channelNo;
        PdfTravelItineraryUrlRequest pdfTravelItineraryUrlRequest = req.getData();
        HashMap<String, String> param = new HashMap<>();
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        param.put("digestmod", digestmodEnum.getName());
        //网站用户id
        param.put("user_id", clientIp);
        //web:电脑上的浏览器；h5:手机上的浏览器，包括移动应用内完全内置的web_view；native：通过原生SDK植入APP应用的方式
        param.put("client_type", req.getData().getClient_type());
        //传输用户请求验证时所携带的IP
        param.put("ip_address", clientIp);
        geetestService.validate(req.getData().getScene(), req.getData(), param);
        ChannelInfo channelInfo = commonService.findChannelInfo(channelCode);
        getAntifraudTask(req, req.getData().getTicketNo(), channelInfo, bizDto);
        MetricLogUtils.saveMetricLog("行程确认单预览-客票提取", req);
        FlightVerifyReq flightVerifyReq = getFlightVerifyReq(pdfTravelItineraryUrlRequest);
        BaseResultDTO<TicketVerifyResponse> invokeResultDTO = getVerifyTicket(flightVerifyReq, clientIp, channelInfo, true);
        if (ObjectUtils.isEmpty(invokeResultDTO) || ObjectUtils.isEmpty(invokeResultDTO.getResult()) ||
                !UnifiedOrderResultEnum.SUCCESS.getResultCode().equals(invokeResultDTO.getResultCode())) {
            MultiLangServiceException.fail(CommonErrorCode.FAIL);
        }
        FlightItineraryInfo flightItineraryInfo = invokeResultDTO.getResult().getData();
        flightItineraryInfo.getDetrSegmentDetailList().forEach(i ->
                {
                    ApiAirPortInfoDto depAirport = cacheService.getLocalAirport(i.getDepAirportCode());
                    i.setDepCityCode(Optional.ofNullable(depAirport).map(ApiAirPortInfoDto::getCityCode).orElse(null));
                    ApiAirPortInfoDto arrAirport = cacheService.getLocalAirport(i.getArrAirportCode());
                    i.setArrCityCode(Optional.ofNullable(arrAirport).map(ApiAirPortInfoDto::getCityCode).orElse(null));

                }
        );
        boolean segMatchFlag = flightItineraryInfo.getDetrSegmentDetailList().stream().anyMatch(
                detrSegmentDetail -> pdfTravelItineraryUrlRequest.getDepCityCode().equalsIgnoreCase(detrSegmentDetail.getDepCityCode())
                        && pdfTravelItineraryUrlRequest.getArrCityCode().equalsIgnoreCase(detrSegmentDetail.getArrCityCode())
                        && pdfTravelItineraryUrlRequest.getFlightNo().equalsIgnoreCase(detrSegmentDetail.getFlightNo())
                        && pdfTravelItineraryUrlRequest.getFlightDate().equalsIgnoreCase(detrSegmentDetail.getDepTime().substring(0, 10)));
        if (!segMatchFlag) {
            ServiceException.fail("航段不匹配");
        }
        String html = getHtml(req.getData().getPassengerName(), false, flightItineraryInfo, req.getLanguage());
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byteArrayInputStream = PdfUtil.exportPdf(html);
        } catch (Exception e) {
            throw new ServiceException("生成pdf失败");
        }
        Date curDate = new Date();
        String monthStr = DateUtil.dateToString(curDate, "yyyy-MM");
        String fileName = MD5Util.encrypt(req.getData().getTicketNo().replace("-", ""))
                + ".pdf";
        String directory = emailConfig.SFTP_itinerary_path + "/" + monthStr;
        Boolean isFile = FavFTPUtil.sftpUploadStream(byteArrayInputStream, directory, fileName, emailConfig);
        if (!isFile) {
            throw new ServiceException("上传pdf失败");
        }
        ResponseData<String> resp = ResponseData.success();
        resp.setData(emailConfig.SFTP_TOMCAT_PORT_01 + emailConfig.SFTP_itinerary_IMG_PATH_01 + "/" + monthStr + "/" + fileName);
        return resp;
    }

    @Override
    public void sendEmailCode(BizDto bizDto, String ffpNo, SendEmailCodeParam sendEmailCodeParam, LanguageEnum language) {
        EmailContent emailContent = commonService.initEmailTemplate(CaptchaFuncEnum.TRAVEL_CONFIRMATION_FORM, language);
        //IP以及卡号验证
        CheckDayLicense ipCheckDayLicense = new CheckDayLicense(bizDto.getIp(), CheckLicenseFuncEnum.TRAVEL_CONFIRMATION_FORM_IP, CommonErrorCode.DEVICE_OVER_LIMIT.name(), CommonErrorCode.DEVICE_OVER_LIMIT.getMessage());
        CheckDayLicense emailCheckDayLicense = new CheckDayLicense(sendEmailCodeParam.getEmail(), CheckLicenseFuncEnum.TRAVEL_CONFIRMATION_FORM_EMAIL, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
        if (StringUtils.isNotBlank(ffpNo)) {
            CheckDayLicense ffpCheckDayLicense = new CheckDayLicense(ffpNo, CheckLicenseFuncEnum.TRAVEL_CONFIRMATION_FORM_FFP, CommonErrorCode.ACCOUNT_OVER_LIMIT.name(), CommonErrorCode.ACCOUNT_OVER_LIMIT.getMessage());
            commonService.checkDayLicense(true, ipCheckDayLicense, emailCheckDayLicense, ffpCheckDayLicense);
        } else {
            commonService.checkDayLicense(true, ipCheckDayLicense, emailCheckDayLicense);
        }
        String sendCode = commonService.getChkCode();
        //邮件验证码
        String redisKey = redisUtil.getConfig().getLocalRedisKey(RedisKeyFormatUtil.createEmailCaptchaKey(sendEmailCodeParam.getEmail(), CaptchaFuncEnum.TRAVEL_CONFIRMATION_FORM.name()));
        redisUtil.set(redisKey, sendCode, 60 * 10L);
        String content = emailContent.getContent();
        content = content.replace("{{code}}", sendCode);
        mailService.sendEmailByCommonEmails(sendEmailCodeParam.getEmail(), emailContent.getSubject(), content, EmailConfigEnum.BOOK);
    }

    private @NotNull FlightVerifyReq getFlightVerifyReq(PdfTravelItineraryUrlRequest pdfTravelItineraryUrlRequest) {
        FlightVerifyReq flightVerifyReq = new FlightVerifyReq();
        flightVerifyReq.setCertNo(pdfTravelItineraryUrlRequest.getTicketNo());
        flightVerifyReq.setPassengerNm(pdfTravelItineraryUrlRequest.getPassengerName());
        flightVerifyReq.setDepCityCode(pdfTravelItineraryUrlRequest.getDepCityCode());
        flightVerifyReq.setArrCityCode(pdfTravelItineraryUrlRequest.getArrCityCode());
        flightVerifyReq.setFlightDate(pdfTravelItineraryUrlRequest.getFlightDate());
        flightVerifyReq.setFlightNo(pdfTravelItineraryUrlRequest.getFlightNo());
        return flightVerifyReq;
    }

    /**
     * @param flightItineraryInfo 客票数据
     * @param english             是否英文模板
     * @return
     */
    private String genTravelItineraryHTML(FlightItineraryInfo flightItineraryInfo, boolean english, LanguageEnum language) {
        ItineraryContext itineraryContext = new ItineraryContext();
        itineraryContext.flightItineraryInfo = flightItineraryInfo;
        itineraryContext.isEnglish = english;
        itineraryContext.language = language;
        itineraryContext.hoLogo = mainPageConfig.getHoLogo();
        itineraryContext.iataLogo = mainPageConfig.getIataLogo();
        return iThymeleafService.createEnItineraryHtml(itineraryContext);
    }

    private void flightItineraryInfoBeanOpt(FlightItineraryInfo info, LanguageEnum language) {
        List<DetrSegmentDetail> detailList = new ArrayList<>();
        for (DetrSegmentDetail detail : info.getDetrSegmentDetailList()) {
            //去除缺口程
            if ("VOID".equalsIgnoreCase(detail.getTicketStatus()) || StringUtils.isBlank(detail.getTicketStatus())) {
                continue;
            }
            ApiAirPortInfoDto depAirportInfo = cacheService.getLocalAirport(detail.getDepAirportCode());
            detail.setDepCityCode(Optional.ofNullable(depAirportInfo).map(ApiAirPortInfoDto::getCityCode).orElse(null));
            detail.setDepAirportName(BffI18nUtils.getAirportName(depAirportInfo, language.name()));
            detail.setDepCityName(BffI18nUtils.getCityName(depAirportInfo, language.name()));
            ApiAirPortInfoDto arrAirportInfo = cacheService.getLocalAirport(detail.getArrAirportCode());
            detail.setArrCityCode(Optional.ofNullable(arrAirportInfo).map(ApiAirPortInfoDto::getCityCode).orElse(null));
            detail.setArrAirportName(BffI18nUtils.getAirportName(arrAirportInfo, language.name()));
            detail.setArrCityName(BffI18nUtils.getCityName(arrAirportInfo, language.name()));
            if (StringUtils.isNotBlank(detail.getDepTime()) && detail.getDepTime().length() == 19 /*yyyy-MM-dd hh:mm:ss*/) {
                detail.setDepTime(detail.getDepTime().substring(0, 16)); //转成yyyy-MM-dd hh:mm
            }
            if (StringUtils.isNotBlank(detail.getArrTime()) && detail.getArrTime().length() == 19 /*yyyy-MM-dd hh:mm:ss*/) {
                detail.setArrTime(detail.getArrTime().substring(0, 16)); //转成yyyy-MM-dd hh:mm
            }
            detail.ticketStatusDesc = EticketStatusEnum.getEnumByCode(detail.getTicketStatus()).getDesc();
            if (StringUtils.isNotBlank(detail.getDepTime()) && StringUtils.isNotBlank(detail.getArrTime())) {
                detail.setDays(DateUtil.diffDays(detail.getDepTime().substring(0, 10), detail.getArrTime().substring(0, 10), DateUtil.YYYY_MM_DD_PATTERN));
            }
            detailList.add(detail);
        }
        info.setDetrSegmentDetailList(detailList);
    }


    public String idMask(String idCardNum, int front, int end) {
        //身份证不能为空
        if (StringUtil.isNullOrEmpty(idCardNum)) {
            return null;
        }
        //需要截取的长度不能大于身份证号长度
        if ((front + end) > idCardNum.length()) {
            return null;
        }
        //需要截取的不能小于0
        if (front < 0 || end < 0) {
            return null;
        }
        //计算*的数量
        int asteriskCount = idCardNum.length() - (front + end);
        StringBuilder asteriskStr = new StringBuilder();
        for (int i = 0; i < asteriskCount; i++) {
            asteriskStr.append("*");
        }
        String regex = "";
        if (isChinese(idCardNum)) {
            regex = "(\\D{" + String.valueOf(front) + "})(\\D+)(\\D{" + String.valueOf(end) + "})";
        } else {
            regex = "(\\w{" + String.valueOf(front) + "})(\\w+)(\\w{" + String.valueOf(end) + "})";
        }
        return idCardNum.replaceAll(regex, "$1" + asteriskStr + "$3");
    }

    public static boolean isChinese(String str) {
        String regEx = "[\\u4e00-\\u9fa5]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        if (m.find())
            return true;
        else
            return false;
    }


    /**
     * 筛选手机号
     */
    private String filterContactInfo(List<MemberContactSoaModel> contactSoaModelList, int type) {
        if (CollectionUtil.isEmpty(contactSoaModelList)) {
            return null;
        }
        for (MemberContactSoaModel contactSoaModel : contactSoaModelList) {
            if (contactSoaModel.getContactType() == type) {
                return contactSoaModel.getContactNumber();
            }
        }
        return null;
    }

}
