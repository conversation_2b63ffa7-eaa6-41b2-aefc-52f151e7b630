package com.juneyaoair.oneorder.mainpage.util;

import com.juneyaoair.horder.dto.electronic.base.ElectronicBaseDTO;
import com.juneyaoair.horder.dto.electronic.base.ElectronicBaseResult;
import com.juneyaoair.mobile.exception.ServiceException;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;

/**
 * <AUTHOR>
 * @Description ElectronicBaseUtil
 * @created 2024/5/10 9:44
 */
public class ElectronicBaseUtil {

    private final static String SUCCESS = "1001";

    private ElectronicBaseUtil() {
    }

    /**
     * 创建请求参数
     * @param channelCode
     * @param ip
     * @param t
     * @return
     * @param <T>
     */
    public static <T> ElectronicBaseDTO<T> createRequest(String channelCode, String ip, T t) {
        return createRequest(channelCode, ip, null, null, t);
    }

    /**
     * 创建请求参数
     * @param channelCode
     * @param ip
     * @param createId
     * @param createName
     * @param t
     * @return
     * @param <T>
     */
    public static <T> ElectronicBaseDTO<T> createRequest(String channelCode, String ip, String createId, String createName, T t) {
        ElectronicBaseDTO<T> baseRequest = new ElectronicBaseDTO<>();
        baseRequest.setRequest(t);
        baseRequest.setIp(StringUtils.isBlank(ip) ? getLocalIp() : ip);
        baseRequest.setVersion("1.0");
        baseRequest.setChannelCode(channelCode);
        baseRequest.setCreateId(null == createId ? null : Long.parseLong(createId));
        baseRequest.setCreateName(createName);
        return baseRequest;
    }

    /**
     * 获取IP
     * @return
     */
    private static String getLocalIp() {
        try {
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            return null == hostAddress ? "127.0.0.1" : hostAddress;
        } catch (Exception var1) {
            return "127.0.0.1";
        }
    }

    /**
     * 解析结果数据
     * @param baseResult
     * @param message
     * @return
     * @param <T>
     */
    public static <T> T getResult(ElectronicBaseResult<T> baseResult, String message){
        if (null == baseResult) {
            throw ServiceException.fail(message);
        }
        if (SUCCESS.equals(baseResult.getResultCode())) {
            return baseResult.getResult();
        }
        throw ServiceException.fail(StringUtils.isBlank(message) ? baseResult.getErrorMsg() : message);
    }

}
