package com.juneyaoair.oneorder.mainpage.controller;

import com.juneyaoair.mobile.exception.MultiLangServiceException;
import com.juneyaoair.mobile.exception.errorcode.CommonErrorCode;
import com.juneyaoair.oneorder.annotation.ApiLog;
import com.juneyaoair.oneorder.api.email.dto.SendEmailCodeParam;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.SceneEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.common.dto.enums.CaptchaFuncEnum;
import com.juneyaoair.oneorder.common.dto.enums.ChannelCodeEnum;
import com.juneyaoair.oneorder.common.dto.enums.SensitiveOperationEnum;
import com.juneyaoair.oneorder.common.service.TongDunService;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.mainpage.dto.sendtravelitinerary.TicketVerifyEmailRequest;
import com.juneyaoair.oneorder.mainpage.dto.travelitinerary.PdfTravelItineraryUrlRequest;
import com.juneyaoair.oneorder.mainpage.dto.verify.FlightVerifyReq;
import com.juneyaoair.oneorder.mainpage.service.IItineraryService;
import com.juneyaoair.oneorder.restresult.response.ResponseData;
import com.juneyaoair.oneorder.ticket.FlightItineraryInfo;
import com.juneyaoair.oneorder.util.LoginCheckUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Api(value = "ItineraryController", tags = "行程确认单")
@RequestMapping("/itinerary")
@RestController
@Slf4j
public class ItineraryController extends BaseController {

    @Resource
    private IItineraryService itineraryService;
    @Autowired
    private IGeetestService geetestService;
    @Autowired
    private TongDunService tongDunService;

    @ApiLog
    @ApiOperation(value = "行程确认单获取", notes = "通过票号、姓名查出行程确认单")//电子行程单机票验真
    @RequestMapping(value = "ticketVerify", method = RequestMethod.POST)
    public ResponseData<FlightItineraryInfo> ticketVerify(@RequestBody @Validated RequestDataDto<FlightVerifyReq> requestData,
                                                          BindingResult bindingResult,
                                                          HttpServletRequest httpReq) {
        //参数检验
        if (bindingResult.hasErrors()) {
            throw MultiLangServiceException.fail(CommonErrorCode.TICKET_INFO_NOT_FOUND,bindingResult.getAllErrors().get(0).getDefaultMessage());
        }
        BizDto bizDto = initBizDto(httpReq);
        // 检查接口是否访问合规
        LoginCheckUtil.checkLogin(requestData, ChannelCodeEnum.G_B2C);
        return itineraryService.ticketVerify(requestData, bindingResult, bizDto);
    }

    @ApiOperation(value = "发送行程确认单邮件", notes = "发送行程确认单邮件")
    @RequestMapping(value = "sendTravelItinerary", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public ResponseData<String> sendTravelItinerary(@RequestBody @Validated RequestDataDto<TicketVerifyEmailRequest> requestDataDto, HttpServletRequest httpReq) {
        LoginCheckUtil.checkLogin(requestDataDto,ChannelCodeEnum.G_B2C);
        BizDto bizDto = initBizDto(httpReq);
        return itineraryService.sendTravelItinerary(requestDataDto,bizDto,httpReq);
    }

    @ApiOperation(value = "发送行程确认单邮箱验证码", notes = "发送行程确认单邮箱验证码")
    @RequestMapping(value = "sendEmailCode", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseData sendEmailCode(@RequestBody @Validated RequestDataDto<SendEmailCodeParam> requestDataDto, HttpServletRequest request){
        LoginCheckUtil.checkLogin(requestDataDto,ChannelCodeEnum.G_B2C);
        //极验验证
        geetestService.validate(SceneEnum.EMAIL_GLOBAL,requestDataDto);
        BizDto bizDto = initBizDto(request);
        //同盾验证
        SendEmailCodeParam emailCaptcha = requestDataDto.getData();
        tongDunService.sendSmsOrEmail(bizDto, requestDataDto.getBlackBox(), null, emailCaptcha.getEmail(), CaptchaFuncEnum.TRAVEL_CONFIRMATION_FORM, SensitiveOperationEnum.SEND_EMAIL_CTRL.name());
        itineraryService.sendEmailCode(bizDto,requestDataDto.getFfpNo(),requestDataDto.getData(),requestDataDto.getLanguage());
        return ResponseData.success();
    }

    @ApiOperation(value = "客票验证：电子客票行程单pdf url", notes = "电子客票行程单pdf url")//电子行程单客票验证
    @RequestMapping(value = "pdfTravelItineraryUrl", method = RequestMethod.POST)
    public ResponseData<String> pdfTravelItineraryData(@RequestBody @Validated RequestDataDto<PdfTravelItineraryUrlRequest> req,
                                                      HttpServletRequest request,
                                                      HttpServletResponse response) {
        BizDto bizDto = initBizDto(request);
        return itineraryService.pdfTravelItineraryUrl(req,bizDto, response);
    }
}
