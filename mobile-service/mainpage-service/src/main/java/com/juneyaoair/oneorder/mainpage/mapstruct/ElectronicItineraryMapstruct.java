package com.juneyaoair.oneorder.mainpage.mapstruct;

import com.juneyaoair.horder.dto.electronic.result.ElectronicInfo;
import com.juneyaoair.oneorder.mainpage.dto.electronic.ElectronicInfoResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Description ElectronicItineraryMapstruct
 * @created 2024/5/13 16:16
 */
@Mapper
public interface ElectronicItineraryMapstruct {

    ElectronicItineraryMapstruct MAPPER = Mappers.getMapper(ElectronicItineraryMapstruct.class);

    /**
     * ElectronicInfo to ElectronicInfoResult
     * @param orderElectronicInfo
     * @return
     */
    ElectronicInfoResult getElectronicInfoResult(ElectronicInfo orderElectronicInfo);

}
