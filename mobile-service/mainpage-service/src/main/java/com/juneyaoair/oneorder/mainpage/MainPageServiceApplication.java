package com.juneyaoair.oneorder.mainpage;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 * EnableFeignClients需要扫描第三方的feign  com.juneyaoair.flightbasic.feign
 */
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class},
        scanBasePackages = "com.juneyaoair")
@EnableDiscoveryClient
@EnableAsync
@EnableApolloConfig(value = {"application","mainpage"})
@EnableFeignClients(basePackages = {"com.juneyaoair"})
public class MainPageServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(MainPageServiceApplication.class, args);
    }
}
