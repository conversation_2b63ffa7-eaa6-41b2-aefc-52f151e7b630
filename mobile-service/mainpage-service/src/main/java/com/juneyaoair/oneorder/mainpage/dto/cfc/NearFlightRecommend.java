package com.juneyaoair.oneorder.mainpage.dto.cfc;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 邻近航班推荐, 热门推荐 航线
 * @date 2019/2/12  10:23.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NearFlightRecommend {

    @ApiModelProperty("出发城市")
    private String depCityCode;

    @ApiModelProperty("出发城市名称")
    private String depCityName;

    @ApiModelProperty("到达城市")
    private String arrCityCode;

    @ApiModelProperty("到达城市名称")
    private String arrCityName;

    @ApiModelProperty("价格")
    private Double price;

    @ApiModelProperty("出发日期")
    private String departureDate;

    @ApiModelProperty("返程日期")
    private String returnDate;

    @ApiModelProperty("航程类型 OW RT")
    private String flightType;

    @ApiModelProperty("国内，国际标志")
    private String tripType;

    @ApiModelProperty(value = "规则类型,1:邻近航线,2:热门推荐", example = "1", notes = "1:邻近航线,2:热门推荐", allowableValues = "1,2")
    private String ruleType;
}
