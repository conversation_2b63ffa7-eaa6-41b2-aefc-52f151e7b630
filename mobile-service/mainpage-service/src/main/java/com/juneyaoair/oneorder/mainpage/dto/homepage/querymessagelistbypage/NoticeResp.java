package com.juneyaoair.oneorder.mainpage.dto.homepage.querymessagelistbypage;

import com.juneyaoair.flightbasic.request.notice.resposne.NoticeResponse;
import com.juneyaoair.oneorder.tools.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Optional;

@Data
public class NoticeResp {
    @ApiModelProperty(value = "消息ID")
    private String messageId;
    @ApiModelProperty(value = "消息标题")
    private String messageTitle;

    @ApiModelProperty(value = "消息类型")
    private String messageType;
    @ApiModelProperty(value = "消息类型")
    private String messagePlainTxt;

    @ApiModelProperty(value = "创建时间")
    private String messageCreateTimeStr;

    @ApiModelProperty("创建时间戳")
    private Long messageCreateTimeStamp;

    @ApiModelProperty(value = "消息标签")
    private MessageTagResp messageTag;

    @ApiModelProperty(value = "移动端消息地址")
    private String messageUrl;

    @ApiModelProperty(value = "官网公告地址")
    private String b2cMessageUrl;

    public static NoticeResp fromNoticeResponse(NoticeResponse dto) {
        NoticeResp ret = new NoticeResp();
        ret.setMessageId(dto.getMessageId());
        ret.setMessageTitle(dto.getMessageTitle());
        ret.setMessagePlainTxt(dto.getMessagePlainTxt());
        ret.setMessageType(dto.getMessageType());
        ret.setMessageCreateTimeStr(dto.getMessageCreateTimeStr());
        if (StringUtils.isNotBlank(ret.messageCreateTimeStr)) {
            Date endTime = DateUtil.toDate(ret.messageCreateTimeStr, DateUtil.YYYY_MM_DD_HH_MM_PATTERN, DateUtil.YYYY_MM_DD_PATTERN);
            if (null != endTime) {
                ret.setMessageCreateTimeStamp(endTime.getTime());
            }
        }
        ret.setMessageTag(Optional.ofNullable(dto.getMessageTag()).map(i -> {
            MessageTagResp tagResp = new MessageTagResp();
            tagResp.setMessageTagName(i.getMessageTagName());
            tagResp.setMessageTagColor(i.getMessageTagColor());
            tagResp.setMessageTagBackgroundColor(i.getMessageTagBackgroundColor());
            return tagResp;
        }).orElse(null));
        ret.setMessageUrl(dto.getMessageUrl());
        ret.setB2cMessageUrl(dto.getB2cMessageUrl());
        return ret;
    }

}
