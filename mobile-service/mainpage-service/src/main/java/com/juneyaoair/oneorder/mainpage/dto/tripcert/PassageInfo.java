package com.juneyaoair.oneorder.mainpage.dto.tripcert;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 行程单航段信息
 */
@ApiModel(description = "行程单航段信息")
@Data
public class PassageInfo {
    @ApiModelProperty("航班号")
    private String flightNo;
    @ApiModelProperty("出发时间")
    private String depTime;
    @ApiModelProperty("到达时间")
    private String arrTime;
    @ApiModelProperty("出发机场code")
    private String depAirPortCode;
    @ApiModelProperty("到达机场code")
    private String arrAirPortCode;
    @ApiModelProperty("出发机场名")
    private String depAirPortName;
    @ApiModelProperty("到达机场名")
    private String arrAirPortName;
    @ApiModelProperty("出发城市code")
    private String depCityCode;
    @ApiModelProperty("到达城市code")
    private String arrCityCode;
    @ApiModelProperty("出发城市名")
    private String depCityName;
    @ApiModelProperty("到达城市名")
    private String arrCityName;
    /**
     * 出发航站楼
     */
    @ApiModelProperty("出发航站楼")
    private String depTerminal;
    /**
     * 到达航站楼
     */
    @ApiModelProperty("到达航站楼")
    private String arrTerminal;
}
