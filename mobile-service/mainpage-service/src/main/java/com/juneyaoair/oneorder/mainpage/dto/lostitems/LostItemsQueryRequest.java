package com.juneyaoair.oneorder.mainpage.dto.lostitems;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;


@ApiModel
@XmlRootElement(name = "LostItemsQueryRequest")
@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class LostItemsQueryRequest {
    @ApiModelProperty("航班号")
    @NotEmpty(message = "航班号不能为空")
    private String lbFlightNo;
    @ApiModelProperty("航班日期")
    @NotEmpty(message = "航班日期不能为空")
    private String lbFlightDate;

    //二级类型
    @ApiModelProperty(hidden = true)
    private String lbTypeNew;

    @ApiModelProperty(value = "类型",example = "" +
            "        101        电子产品\n" +
            "        102        现金、钱包\n" +
            "        103        手表、金银首饰等物品\n" +
            "        201        证件、银行卡等\n" +
            "        301        充电宝等锂电池\n" +
            "        302        服饰、箱包类物品\n" +
            "        303        食品\n" +
            "        304        其他")
    private String lbType;


    @ApiModelProperty(hidden = true)
    private String ffpId;
    @ApiModelProperty(hidden = true)
    private String channelCode;

}

