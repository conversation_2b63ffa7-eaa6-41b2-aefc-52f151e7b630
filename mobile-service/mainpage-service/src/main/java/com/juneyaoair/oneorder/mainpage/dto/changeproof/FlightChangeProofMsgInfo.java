package com.juneyaoair.oneorder.mainpage.dto.changeproof;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName FlightChangeProofMsgInfo
 * @Description 航变证明邮件替换内容
 * <AUTHOR>
 * @Date 2019/6/27 15:04
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FlightChangeProofMsgInfo {

    /**
     * 旅客姓名
     */
    private String passengerName;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 日期
     */
    private String flightDate;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 出发城市
     */
    private String orgCity;

    /**
     * 出发城市（英）
     */
    private String orgEnCity;

    /**
     * 到达城市
     */
    private String dstCity;

    /**
     * 到达城市（英）
     */
    private String dstEnCity;

    /**
     * 计划起飞时间
     */
    private String depTime;

    /**
     * 计划起飞时间（英）
     */
    private String enDepTime;

    /**
     * 计划到达时间
     */
    private String arrTime;

    /**
     * 计划到达时间（英）
     */
    private String enArrTime;

    /**
     * 实际起飞时间
     */
    private String actDepTime;

    /**
     * 实际起飞时间（英）
     */
    private String enActDepTime;

    /**
     * 实际到达时间
     */
    private String actArrTime;

    /**
     * 实际到达时间（英）
     */
    private String enActArrTime;

    /**
     * 离港延误标记
     */
    private String delay;

    /**
     * 到港延误标记
     */
    private String arrDelay;
    /**
     * 航班备降
     */
    private String altLanding;

    /**
     * 航班取消
     */
    private String cancel;

    /**
     * 公司原因
     */
    private boolean gongsi;

    /**
     * 非公司原因
     */
    private boolean feigongsi;

    /**
     * 天气原因
     */
    private boolean tianqi;

    /**
     * 空管
     */
    private boolean kongguan;

    /**
     * 机场
     */
    private boolean jichang;

    /**
     * 旅客
     */
    private boolean lvke;

    /**
     * 军事活动
     */
    private boolean junshihuodong;

    /**
     * 航班时刻安排
     */
    private boolean shikeanpai;

    /**
     * 联检
     */
    private boolean lianjian;

    /**
     * 油料
     */
    private boolean youliao;

    /**
     * 离港系统
     */
    private boolean ligang;

    /**
     * 	公共安全
     */
    private boolean gonggonganquan;

    /**
     * 待定
     */
    private boolean daiding;

    /**
     * 当前时间
     */
    private String currentDateStr;
    /**
     * 航延编号
     */
    private String delayNum;

    /**
     * 流量控制 2020-11-03
     */
    private boolean liuliang;
    /**
     * 出发城市时区 2020-11-03
     */
    private Integer depCityZone;
    /**
     * 到达城市时区 2020-11-03
     */
    private Integer arrCityZone;


    /**
     * 滑入日期
     */
    public String flightInnDate;

    /**
     * 滑入时间
     */
    public String flightInnTime;
    /**
     * 滑出日期
     */
    public String flightOutDate;
    /**
     * 滑出时间
     */
    public String flightOutTime;
}
