<!DOCTYPE html>
<!-- saved from url=(0072)http://www.juneyaoair.com/pages/ReserveManage/TravelItinerary_print.aspx -->
<!--<html xmlns="http://www.w3.org/1999/xhtml">-->

<!--<!DOCTYPE html>-->
<html lang="en" xmlns:th="http://wwww.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>
        欢迎访问吉祥航空官方网站
    </title>
    <link rel="shortcut icon" href="https://mediaws.juneyaoair.com/b2c/images/favicon.ico"/>
    <style>
        @page{size:250mm 353.5mm;margin:0;padding:0;}
        body {
            font-family: SimSun;
        }
        /*延误证明*/
        .delay_print {
            position: relative;
            width: 900px;
            margin: 0 auto;
            background: #fff;
            overflow: hidden;
            padding-left: 15px;
            padding-right: 15px;
        }

        .delay_print_logo {
            margin-top: 50px;
        }

        .delay_print_icon {
            position: absolute;
            top: 76px;
            right: 64px;
        }

        .delay_print_text {
            text-align: center;
            margin-top: 10px;
            border-top: 1px solid #bcc0c1;
        }

        .delay_print_text h3 {
            font-size: 24px;
        }

        .delay_print_text .content {
            margin-top: 15px;
            font-size: 12px;
            text-indent: 2em;
            text-align: center;
            color: #b2b2b2;
        }

        .delay_print_content {
            margin: 0 auto;
            margin-top: 45px;
            color: black;
            font-size: 14px;
        }

        .delay_print_content table {
            width: 100%;
        }

        .delay_print_content table td {
            padding: 5px 5px;
            line-height: 22px;
            width: 200px;
        }

        .delay_print_content table td.text-right {
            text-align: left;
            width: 100px;
        }

        .delay_print_content table td table.sub td {
            padding: 2px 0;
        }

        .delay_print_content table td table.sub td.current {
        }

        .delay_print_describe {
            padding: 0 6px;
            margin-top: 10px;
            line-height: 22px;
            margin-bottom: 100px;
        }

        .delay_print_stamp {
            /*position: relative;*/
            width: 100%;
            height: 230px;
            margin-top: 72px;
        }

        .delay_print_stamp img {
            position: absolute;
            right: 170px;
            border-bottom: 0;
        }

        .delay_print_stamp .date {
            position: absolute;
            right: 170px;
            /*top: 150px;*/
            bottom: 50px;
        }
    </style>
</head>
<body id="content">
<div class="delay_print" style="height: 100%">
    <div>
        <div class="delay_print_logo">
            <img th:src="${hoLogo}" alt=""/>
            <span style="float: right;padding-top: 60px;font-size: 16px;">服务热线：95520</span>
        </div>
        <div class="delay_print_logo" style="border-top: 2px solid #8d2946;padding-top: 50px;margin-top: 10px;">
            <img th:src="${iataLogo}" alt=""/>
        </div>
        <div class="delay_print_text">
            <h3>行程确认单</h3>
        </div>
        <div class="delay_print_content">
            <table>
                <tbody>
                <tr class="bg">
                    <td class="text-right">航空公司记录编号:
                    </td>
                    <td class="text-right" style="margin-left: 260px;" th:text="'订座记录编号：'+${pnr}"></td>
                </tr>
                <tr>
                    <td class="text-right" th:text="'旅客姓名：'+${passengerName}"></td>
                    <td class="text-right" style="margin-left: 260px;" th:text="'票号：'+${ticketNo}"></td>
                </tr>
                <tr class="bg">

                    <td class="text-right" th:text="'证件号：'+${certNo}"></td>
                    <td class="text-right" style="margin-left: 260px;" th:text="'联票：'+${followTicketNo}"></td>
                </tr>
                <tr>
                    <td class="text-right">出票航空公司：JUNEYAO AIR</td>
                    <td class="text-right" style="margin-left: 260px;" th:text="'出票时间：'+${issueDate}">
                    </td>
                </tr>
                <tr class="bg">

                    <td class="text-right" style="margin-left: 260px;" th:text="'航协代码：'+${iataNo}"></td>
                </tr>

                <tr>
                    <td colspan="2">
                        <table style="border-top: 2px solid #bcc0c1; border-bottom: 2px solid #bcc0c1; text-align: center;">
                            <tr class="bg" >
                                <td style="width: 160px;">始发地/目的地</td>
                                <td style="width: 90px;">航班</td>
                                <td style="width: 98px;">舱位</td>
                                <td style="width: 90px;">日期</td>
                                <td style="width: 90px;">起飞时间</td>
                                <td style="width: 90px;">到达时间</td>

                                <td style="width: 100px;">客票状态</td>
                                <td style="width: 90px;">行李</td>
                                <td style="width: 110px;">航站楼<br/>
                                    起飞 到达
                                </td>
                            </tr>
                            <tbody>
                            <tr class="bg" th:each="seg:${segments}">
                                <td style="width: 130px;" th:utext="${seg.getDepAirport()}+'<br/>'+${seg.getArrAirport()}"></td>
                                <td style="width: 90px;" th:text="${seg.getFlightNo()}"></td>
                                <td style="width: 90px;" th:text="${seg.getCabinCode()}"></td>
                                <td style="width: 90px;" th:text="${seg.getFlightDate()}"></td>
                                <td style="width: 90px;" th:text="${seg.getDepTime()}"></td>
                                <td style="width: 90px;" th:text="${seg.getArrTime()}+${seg.getDays()}"></td>
                                <td style="width: 90px;" th:text="${seg.getSegmentStatus()}"></td>
                                <td style="width: 90px;" th:text="${seg.getBaggageWeight()}"></td>
                                <td style="width: 100px;" th:text="${seg.getDepAirportTerminal()}+'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'+${seg.getArrAirportTerminal()}"></td>
                            </tr>

                            </tbody>
                        </table>
                    </td>
                </tr>

                <tr class="bg">
                    <td class="text-right" th:text="'付款方式：'+${payMethod}">
                    </td>
                    <td class="text-right" style="margin-left: 260px;">
                        <label style="float: left;">
                            税款：</label>
                        <div style="float: left;" th:text="${tax}">
                            ${tax}<br/>


                        </div>
                    </td>
                </tr>
                <tr class="bg">
                    <td class="text-right" th:text="'机票款：'+${fare}"></td>
                </tr>
                <tr class="bg">
                    <td class="text-right" th:text="'总额：'+${totalAmount}"></td>
                </tr>
                <tr class="bg">
                    <td class="text-right" colspan="2" th:text="'限制条件：'+${signingInfo}">
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="delay_print_describe">
                温馨提示：<br/>
                <div style="color: #d0cece; border-top: 1px solid #d0cece; margin-top: 6px; padding-top: 6px;">
                    1.此单可办理签证，但不作为国内报销凭证<br/>
                    2.如您已使用电子客票，在客票所有航段使用27天内可验证。如果电子客票未被使用，在客票购买当天起一年内可以提供验证。
                </div>
            </div>
        </div>
    </div>
</div>
<div id="tips" style="display: none;" class="print"></div>
</body>
</html>