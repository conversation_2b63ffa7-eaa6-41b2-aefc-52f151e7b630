<!DOCTYPE html>
<!-- saved from url=(0072)http://www.juneyaoair.com/pages/ReserveManage/TravelItinerary_print.aspx -->

<!--<html xmlns="http://www.w3.org/1999/xhtml">-->

<html lang="en" xmlns:th="http://wwww.thymeleaf.org">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>
        欢迎访问吉祥航空官方网站
    </title>
    <link rel="shortcut icon" href="http://mediaws.juneyaoair.com/b2c/images/favicon.ico"/>
    <style>
        @page{size:250mm 353.5mm;margin:0;padding:0;}
        body {font-family: SimSun;}
        .delay_print {position: relative; width: 900px; margin: 0 auto; background: #fff; overflow: hidden; padding-left: 15px;padding-right: 15px;}
        .delay_print_logo {margin-top: 35px;}
        .delay_print_icon {position: absolute; top: 76px;right: 64px;}
        .delay_print_text {text-align: center;border-top: 1px solid #bcc0c1;}
        .delay_print_text h3 {font-size: 24px;}
        .delay_print_text .content {margin-top: 15px;font-size: 12px;text-indent: 2em;text-align: center;color: #b2b2b2;}
        .delay_print_content {margin: 0 auto;margin-top: 45px;color: black;font-size: 14px;}
        .delay_print_content table {width: 100%;}
        .delay_print_content table td {padding: 5px 5px;line-height: 22px;width: 200px;}
        .delay_print_content table td.text-right{text-align: left;width: 100px;color: #000000;}
        .delay_print_content table td.text-right .bd{font-weight: bold;}
        .delay_print_content table td table.sub td {padding: 2px 0;}
        .delay_print_content table td table.sub td.current {}
        .delay_print_describe {padding: 0 6px;margin-top: 10px;line-height: 22px;margin-bottom: 100px;color: #000000;}
        .delay_print_describe .title{font-weight: bold;}
        .delay_print_stamp {width: 100%;height: 230px;margin-top: 72px;}
        .delay_print_stamp img {position: absolute;right: 170px;border-bottom: 0;}
        .delay_print_stamp .date {position: absolute;right: 170px;bottom: 50px;}
    </style>
</head>
<body id="content">
<div class="delay_print" style="height:100%">
    <div>
        <div class="delay_print_logo">
            <img th:src="${iataLogo}" alt="" />
        </div>
        <div class="delay_print_text">
            <h3>旅行確認書</h3>
        </div>
        <div class="delay_print_content">
            <table>
                <tbody>
                <tr class="bg">
                    <td class="text-right"><span class="bd" th:text = "'航空会社レコード: '+${pnr}"></span></td>
                    <td class="text-right" style="margin-left:260px;"><span class="bd" th:text="'予約レコード: '+${pnr}"></span></td>
                </tr>
                <tr>
                    <td class="text-right"><span class="bd" th:text="'名前: '+${passengerName}"></span></td>
                    <td class="text-right" style="margin-left:260px;"><span class="bd" th:text="'電子チケット番号: '+${ticketNo}"></span></td>
                </tr>
                <tr class="bg">
                    <td class="text-right"><span class="bd" th:text="'書類番号: '+${certNo}"></span></td>
                    <td class="text-right" style="margin-left:260px;"><span class="bd" th:text="'関連チケット番号: '+${followTicketNo}"></span></td>
                </tr>
                <tr>
                    <td class="text-right"><span class="bd">航空会社: JUNEYAO AIR</span></td>
                    <td class="text-right" style="margin-left:260px;"><span class="bd" th:text="'発券期日: '+${issueDate}"></span> </td>
                </tr>
                <tr>
                  <!--  <td class="text-right"><span class="bd">ISSUING AGENT: </span>{agent}</td>-->
                    <td class="text-right"><span class="bd">発券エージェント: </span></td>
                    <td class="text-right" style="margin-left:260px;"><span class="bd" th:text="'IATAコード: '+${iataNo}"></span></td>
                </tr>
                <tr class="bg">
                   <!-- <td class="text-right" style="margin-left:260px"><span class="bd">AGENCY ADDRESS: </span>{北京乐涂国际旅行社}</td>-->
                    <td class="text-right" style="margin-left:260px"><span class="bd">エージェントの所在地: </span></td>
                </tr>
                <tr>
                   <!-- <td class="text-right"><span class="bd">TEL: </span>{075536560169}</td>-->
                    <td class="text-right"><span class="bd">連絡先: </span></td>
                   <!-- <td class="text-right" style="margin-left:260px;"><span class="bd">FAX: </span>{075536560169} </td>-->
                    <td class="text-right" style="margin-left:260px;"><span class="bd">ファクス番号: </span> </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <table style="border-top: 1px solid #CCCCCC; border-bottom: 1px solid #CCCCCC; text-align: center;margin-top: 30px">
                            <tr class="bg">
                                <td style="width:160px;color: #000000;">出発/到着 </td>
                                <td style="width:90px;color: #000000;">フライト</td>
                                <td style="width:98px;color: #000000;">クラス</td>
                                <td style="width:90px;color: #000000;">出発日付</td>
                                <td style="width:90px;color: #000000;">出発時刻</td>
                                <td style="width:90px;color: #000000;">到着時刻</td>
                                <!--<td style="width:100px;color: #000000;">PERIOD</td>-->
                                <td style="width:100px;color: #000000;">航空券状態</td>
                                <td style="width:90px;color: #000000;">手荷物制限</td>
                                <td style="width:200px;color: #000000;">ターミナルビル <br/> 出発  到着 </td>
                            </tr>
                            <tbody>
                            <tr class="bg" th:each="seg:${segmentInfos}">
                                <td style="width: 130px;" th:utext="${seg.getDepAirport()}+'<br/>'+${seg.getArrAirport()}"></td>
                                <td style="width: 90px;" th:text="${seg.getFlightNo()}"></td>
                                <td style="width: 90px;" th:text="${seg.getCabinCode()}"></td>
                                <td style="width: 90px;" th:text="${seg.getFlightDate()}"></td>
                                <td style="width: 90px;" th:text="${seg.getDepTime()}"></td>
                                <td style="width: 90px;" th:text="${seg.getArrTime()}+${seg.getDays()}"></td>
                                <td style="width: 90px;" th:text="${seg.getSegmentStatus()}"></td>
                                <td style="width: 90px;" th:text="${seg.getBaggageWeight()}"></td>
                                <td style="width: 100px;" th:text="${seg.getDepAirportTerminal()}+'&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'+${seg.getArrAirportTerminal()}"></td>
                            </tr>
                            </tbody>
                        </table> </td>
                </tr>
                <tr class="bg">
                    <td class="text-right" style="margin-left:260px"><span class="bd" th:text="'運賃情報: '+${fareCompute}"></span><br/></td>
                </tr>

                <br/>
                <tr style="margin-top: 20px;">
                    <td class="text-right"><span class="bd" th:text="'支払い方法: '+${payMethod}"></span></td>
                    <td class="text-right" style="margin-left:260px;"><span class="bd" th:text="'税金: '+${englishTax}"></span> </td>
                </tr>

                <tr class="bg">
                    <td class="text-right" style="margin-left:260px"><span class="bd" th:text="'航空券料金: '+${fare}"></span></td>
                </tr>

                <tr class="bg">
                    <td class="text-right" style="margin-left:260px"><span class="bd" th:text="'合計: '+${totalAmount}"></span></td>
                </tr>

                <tr class="bg">
                    <td class="text-right" style="margin-left:260px"><span class="bd" th:text="'制限: '+${signingInfo}"></span></td>
                </tr>
                </tbody>
            </table>
            <div class="delay_print_describe">
                <div class="title">注意事項: </div>
                <div style="color:#000000;">
                    · 空港には航空会社が定めた搭乗時間前に到着してください<br/>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="tips" style="display:none;" class="print"></div>
</body>
</html>