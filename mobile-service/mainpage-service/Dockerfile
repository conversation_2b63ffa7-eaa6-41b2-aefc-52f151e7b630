# 构建镜像，执行命令：【docker build -t call-coupon:2.0 .】
#FROM openjdk:8u212-jre
FROM harbor.hoair.cn/library/openjdk:8-jre
MAINTAINER Arber C.

# 时区问题
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone

ADD target/mainpage-service.jar /app/app.jar

ENV JAVA_OPTS="-Xms1024M -Xmx1024M -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=256m"

#ENTRYPOINT ["java", "-server", "-Xms2048M", "-Xmx2048M", "-XX:MetaspaceSize=256m", "-XX:MaxMetaspaceSize=256m","-Dfile.encoding=UTF-8", "-XX:+HeapDumpOnOutOfMemoryError", "-jar", "/app/app.jar"]
ENTRYPOINT ["sh","-c","exec java -server ${JAVA_OPTS} -Dfile.encoding=UTF-8 -XX:+HeapDumpOnOutOfMemoryError -jar /app/app.jar"]


