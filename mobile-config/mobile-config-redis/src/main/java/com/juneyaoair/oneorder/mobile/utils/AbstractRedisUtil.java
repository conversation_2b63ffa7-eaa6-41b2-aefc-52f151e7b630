package com.juneyaoair.oneorder.mobile.utils;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson2.TypeReference;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.BoundListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2023/7/19 16:58
 */
@Slf4j
public abstract class AbstractRedisUtil {
    /**
     * 由具体的实现类注入
     * @return
     */
    protected abstract RedisTemplate getTemplate();
    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                getTemplate().expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        return getTemplate().getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return getTemplate().hasKey(key);
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                getTemplate().delete(key[0]);
            } else {
                getTemplate().delete(CollectionUtils.arrayToList(key));
            }
        }
    }

    //============================String=============================

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return key == null ? null : getTemplate().opsForValue().get(key);
    }

    public String getStr(String key){
        if(key == null){
            return null;
        }
        Object obj = getTemplate().opsForValue().get(key);
        if(obj == null){
            return null;
        }
        return (String) obj;
    }

    /**
     * 设置缓存对象
     * @param key
     * @param value
     * @param expireTime
     * @return
     */
    public boolean setJSON(final String key, Object value, Long expireTime) {
        try {
            String json = JSON.toJSONString(value);
            getTemplate().opsForValue().set(key, json);
            getTemplate().expire(key, expireTime, TimeUnit.SECONDS);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取缓存对象
     * @param key
     * @param type
     * @return
     * @param <T>
     */
    public <T> T getObject(final String key, TypeReference<T> type) {
        String redisStr = (String)get(key);
        if (StringUtils.isBlank(redisStr)) {
            return null;
        }
        return JSON.parseObject(redisStr, type);
    }

    /**
     * 获取缓存对象
     * @param key
     * @param clazz
     * @return
     * @param <T>
     */
    public <T> T getObject(final String key, Class<T> clazz) {
        String redisStr = (String)get(key);
        if (StringUtils.isBlank(redisStr)) {
            return null;
        }
        return JSON.parseObject(redisStr, clazz);
    }

    /**
     * 计数器
     *
     * @param key
     * @param value
     * @return
     */
    public long increment(String key, Long value) {
        return getTemplate().opsForValue().increment(key, value);
    }

    /**
     * 计数器
     *
     * @param key
     * @param value
     * @param timeout 超时时间 单位:秒
     * @return
     */
    public long increment(String key, Long value, long timeout) {
        long count = getTemplate().opsForValue().increment(key, value);
        if (timeout > 0L) {
            getTemplate().expire(key, timeout, TimeUnit.SECONDS);
        }
        return count;
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            getTemplate().opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                getTemplate().opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return
     */
    public long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return getTemplate().opsForValue().increment(key, -delta);
    }

    //================================Map=================================

    /**
     * HashGet
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return 值
     */
    public Object hget(String key, String item) {
        return getTemplate().opsForHash().get(key, item);
    }

    /**
     * 获取指定数据
     * @param key
     * @param hashKey
     * @param c
     * @param <T>
     * @return
     */
    public <T> T hget(final String key, final String hashKey, Class<T> c) {
        T result = null;
        Object obj = getTemplate().opsForHash().get(key, hashKey);
        if (null != obj) {
            result = JSON.parseObject((String) obj, c);
        }
        return result;
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hmget(String key) {
        return getTemplate().opsForHash().entries(key);
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    public boolean hmset(String key, Map<String, Object> map) {
        try {
            getTemplate().opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public <T> boolean hmset2(String key, Map<String, T> map, long time) {
        try {
            getTemplate().<String, T>opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            getTemplate().opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value) {
        try {
            getTemplate().opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value, long time) {
        try {
            getTemplate().opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 删除hash表中的值
     *
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public void hdel(String key, Object... item) {
        getTemplate().opsForHash().delete(key, item);
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    public boolean hHasKey(String key, String item) {
        return getTemplate().opsForHash().hasKey(key, item);
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     * @return
     */
    public double hincr(String key, String item, double by) {
        return getTemplate().opsForHash().increment(key, item, by);
    }

    /**
     * hash递减
     *
     * @param key  键
     * @param item 项
     * @param by   要减少记(小于0)
     * @return
     */
    public double hdecr(String key, String item, double by) {
        return getTemplate().opsForHash().increment(key, item, -by);
    }

    //============================set=============================

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     * @return
     */
    public Set<Object> sGet(String key) {
        try {
            return getTemplate().opsForSet().members(key);
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return null;
        }
    }

    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true 存在 false不存在
     */
    public boolean sHasKey(String key, Object value) {
        try {
            return getTemplate().opsForSet().isMember(key, value);
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSet(String key, Object... values) {
        try {
            return getTemplate().opsForSet().add(key, values);
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return 0;
        }
    }

    /**
     * 将set数据放入缓存
     *
     * @param key    键
     * @param time   时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = getTemplate().opsForSet().add(key, values);
            if (time > 0) {
                expire(key, time);
            }
            return count;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return 0;
        }
    }

    /**
     * 获取set缓存的长度
     *
     * @param key 键
     * @return
     */
    public long sGetSetSize(String key) {
        try {
            return getTemplate().opsForSet().size(key);
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return 0;
        }
    }

    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 移除的个数
     */
    public long setRemove(String key, Object... values) {
        try {
            Long count = getTemplate().opsForSet().remove(key, values);
            return count;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return 0;
        }
    }
    //===============================list=================================

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     * @return
     */
    public List<Object> lGet(String key, long start, long end) {
        try {
            return getTemplate().opsForList().range(key, start, end);
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return null;
        }
    }

    /**
     * 获取list缓存的长度
     *
     * @param key 键
     * @return
     */
    public long lGetListSize(String key) {
        try {
            return getTemplate().opsForList().size(key);
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return 0;
        }
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return
     */
    public Object lGetIndex(String key, long index) {
        try {
            return getTemplate().opsForList().index(key, index);
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return null;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public boolean lSet(String key, Object value) {
        try {
            getTemplate().opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, Object value, long time) {
        try {
            getTemplate().opsForList().rightPush(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public boolean lSet(String key, List<Object> value) {
        try {
            getTemplate().opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, List<Object> value, long time) {
        try {
            getTemplate().opsForList().rightPushAll(key, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     * @return
     */
    public boolean lUpdateIndex(String key, long index, Object value) {
        try {
            getTemplate().opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return false;
        }
    }

    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */
    public long lRemove(String key, long count, Object value) {
        try {
            Long remove = getTemplate().opsForList().remove(key, count, value);
            return remove;
        } catch (Exception e) {
            log.error("redis操作错误", e);
            return 0;
        }
    }

    /**
     * 移除某个key
     *
     * @param key  键
     * @return
     */
    public void remove(String key) {
        try {
            getTemplate().delete(key);
        } catch (Exception e) {
            log.error("redis操作错误", e);
        }
    }

    /**
     * 使用Redis的消息队列
     *
     * @param channel
     * @param message 消息内容
     */
    public void convertAndSend(String channel, Object message) {
        getTemplate().convertAndSend(channel, message);
    }


    /**
     * 根据起始结束序号遍历Redis中的list
     *
     * @param listKey
     * @param start   起始序号
     * @param end     结束序号
     * @return
     */
    public List<Object> rangeList(String listKey, long start, long end) {
        //绑定操作
        BoundListOperations<String, Object> boundValueOperations = getTemplate().boundListOps(listKey);
        //查询数据
        return boundValueOperations.range(start, end);
    }

    /**
     * 弹出右边的值 --- 并且移除这个值
     *
     * @param listKey
     */
    public Object rifhtPop(String listKey) {
        //绑定操作
        BoundListOperations<String, Object> boundValueOperations = getTemplate().boundListOps(listKey);
        return boundValueOperations.rightPop();
    }
}
