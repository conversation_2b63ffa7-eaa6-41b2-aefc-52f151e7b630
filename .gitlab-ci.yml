workflow:
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      variables:
        BUILD_CMD: mvn clean package -Dmaven.test.skip=true -Denforcer.skip=true -Ptest -e -C -U
        IMAGE_TAG: alpha
    - if: '$CI_COMMIT_BRANCH == "uat"'
      variables:
        BUILD_CMD: mvn clean package -Dmaven.test.skip=true -Puat -U -e
        IMAGE_TAG: beta
    - if: '$CI_COMMIT_TAG'
      variables:
        #BUILD_CMD: mvn clean package -q -U -Dmaven.test.skip=true -Denforcer.skip=true -Pprod -e
        BUILD_CMD: mvn clean package -q -U -Dmaven.test.skip=true -Pprod -e
        IMAGE_TAG: $CI_COMMIT_TAG

stages:
  - build
  - docker_image
  - quality_check
  
# 将 Nexus 仓库地址添加到 settings.xml 中
before_script:
  - echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
              xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                                  http://maven.apache.org/xsd/settings-1.0.0.xsd">
                <servers>
                    <server>
                        <id>maven-release</id>
                        <username>deploy</username>
                        <password>deploy</password>
                </server>
                </servers>

              <mirrors>
                  <mirror>
                      <id>nexus-repo-mirror</id>
                  <mirrorOf>*</mirrorOf>
                  <url>http://nexus.juneyaoair.com:8081/repository/maven-public/</url>
                  </mirror>
              </mirrors>
            </settings>'> /root/.m2/settings.xml

# SonarQube代码质量检查
sonarqube-check:
  stage: quality_check
  image: harbor.hoair.cn/base/maven:3.8.5-openjdk-17
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  before_script:
    - echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
      http://maven.apache.org/xsd/settings-1.0.0.xsd">
      <servers>
      <server>
      <id>maven-release</id>
      <username>deploy</username>
      <password>deploy</password>
      </server>
      </servers>
      
      <mirrors>
      <mirror>
      <id>nexus-repo-mirror</id>
      <mirrorOf>*</mirrorOf>
      <url>http://nexus.juneyaoair.com:8081/repository/maven-public/</url>
      </mirror>
      </mirrors>
      </settings>'> /root/.m2/settings.xml
  script:
    - pwd
    - ls
    - mvn verify sonar:sonar -q -Dsonar.projectKey=ho-mobile-bff
  allow_failure: true
  only:
    - develop
  tags:
    - ***********-docker
  when: manual

# 运行 Maven 编译命令
trigger_build:
  #image: maven:3.9.4-eclipse-temurin-8
  image: harbor.hoair.cn/base/maven:3.9.4-eclipse-temurin-8
  stage: build
  script:
    - echo "编译 JAR 包..."
    - $BUILD_CMD
    - echo "JAR 包编译完成。"
  artifacts:
    paths:
      - '**/target/*.jar'
      - '**/Dockerfile'
    expire_in: 1 hour
  tags:
    - build

trigger_push:
  needs: [trigger_build]
  stage: docker_image
  variables:
    GIT_DEPTH: 0
  before_script:
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin $CI_REGISTRY
  script:
    - echo "docker image build and push"
    - docker build -f mobile-service/accountbff-service/Dockerfile -t $CI_REGISTRY_IMAGE/accountbff:$IMAGE_TAG mobile-service/accountbff-service
    - docker build -f mobile-service/bookbff-service/Dockerfile -t $CI_REGISTRY_IMAGE/bookbff:$IMAGE_TAG mobile-service/bookbff-service
    - docker build -f mobile-service/cuss-service/Dockerfile -t $CI_REGISTRY_IMAGE/cuss:$IMAGE_TAG mobile-service/cuss-service
    - docker build -f mobile-service/mainpage-service/Dockerfile -t $CI_REGISTRY_IMAGE/mainpage:$IMAGE_TAG mobile-service/mainpage-service
    - docker build -f mobile-auth/Dockerfile -t $CI_REGISTRY_IMAGE/auth:$IMAGE_TAG mobile-auth
    - docker build -f mobile-service/orderbff-service/Dockerfile -t $CI_REGISTRY_IMAGE/orderbff:$IMAGE_TAG mobile-service/orderbff-service
    - docker build -f mobile-service/searchbff-service/Dockerfile -t $CI_REGISTRY_IMAGE/searchbff:$IMAGE_TAG mobile-service/searchbff-service
    - docker build -f mobile-gateway/Dockerfile -t $CI_REGISTRY_IMAGE/gateway:$IMAGE_TAG mobile-gateway
    - docker build -f mobile-hocar-gateway/Dockerfile -t $CI_REGISTRY_IMAGE/hocargateway:$IMAGE_TAG mobile-hocar-gateway
    - docker build -f hocar-gateway2b/Dockerfile -t $CI_REGISTRY_IMAGE/hocargateway2b:$IMAGE_TAG hocar-gateway2b
    - docker build -f hocar-auth2b/Dockerfile -t $CI_REGISTRY_IMAGE/hocarauth2b:$IMAGE_TAG hocar-auth2b
    - docker build -f mobile-sso/Dockerfile -t $CI_REGISTRY_IMAGE/sso:$IMAGE_TAG mobile-sso
    - docker push $CI_REGISTRY_IMAGE/accountbff:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/bookbff:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/cuss:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/mainpage:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/auth:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/orderbff:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/searchbff:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/gateway:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/hocargateway:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/hocargateway2b:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/hocarauth2b:$IMAGE_TAG
    - docker push $CI_REGISTRY_IMAGE/sso:$IMAGE_TAG

  allow_failure: false
  tags:
    - ***********-shell



