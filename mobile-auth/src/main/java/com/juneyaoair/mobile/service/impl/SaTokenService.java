package com.juneyaoair.mobile.service.impl;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.SaTokenInfo;
import cn.dev33.satoken.stp.StpUtil;
import com.juneyaoair.mobile.dto.LoginResult;
import com.juneyaoair.mobile.service.ITokenService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description  本服务使用公共的saToken框架服务
 * @date 2024/2/4 9:42
 */
@Service
public class SaTokenService implements ITokenService {
    @Override
    public LoginResult createToken(String channelCode,String ffpId,String ffpNo,Long timeout) {
        //根据不同渠道设置token有效期
        SaLoginModel saLoginModel = new SaLoginModel()
                .setDevice(channelCode)
                .setExtra("ffpId",ffpId)
                .setExtra("ffpNo",ffpNo);
        if(timeout != null){
            saLoginModel.setTimeout(timeout);
        }
        StpUtil.login(ffpNo,saLoginModel);
        SaTokenInfo saTokenInfo = StpUtil.getTokenInfo();
        LoginResult loginResult = new LoginResult();
        loginResult.setAccessToken(saTokenInfo.getTokenValue());
        loginResult.setExpiresIn(saTokenInfo.getTokenTimeout());
        loginResult.setFfpNo(ffpNo);
        loginResult.setLoginVersion("1");
        return loginResult;
    }
}
