package com.juneyaoair.mobile.service;

import com.juneyaoair.oneorder.api.common.CommonService;
import com.juneyaoair.oneorder.api.crm.service.ICaptchaService;
import com.juneyaoair.oneorder.api.crm.service.IMemberLoginService;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.crm.dto.Header;
import com.juneyaoair.oneorder.crm.dto.PtApiCRMRequest;
import com.juneyaoair.oneorder.mobile.config.GeetestPropertiesConfig;
import com.juneyaoair.oneorder.mobile.dto.ChannelInfo;
import com.juneyaoair.oneorder.tools.utils.HoAirUuidUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/31 10:52
 */
@Service
@Slf4j
public class LoginService extends CommonService {
    @Autowired
    private GeetestPropertiesConfig geetestPropertiesConfig;
    @Autowired
    private IMemberLoginService memberLoginService;
    @Autowired
    private ICaptchaService captchaService;
    @Autowired
    private IGeetestService geetestService;
    @Autowired
    private ITokenService tokenService;

    /**
     * 调用会员系统注销登录态
     * @param bizDto
     * @param ffpId
     * @param uscka
     */
    public void loginout(BizDto bizDto,String ffpId,String uscka){
        ChannelInfo channelInfo = findChannelInfo(bizDto.getHeadChannelCode());
        Header header = Header.builder()
                .ClientIP(bizDto.getIp())
                .MemberId(Long.valueOf(ffpId))
                .Token(uscka)
                .Timestamp(System.currentTimeMillis())
                .build();
        PtApiCRMRequest ptApiCRMRequest = PtApiCRMRequest.builder()
                .requestId(HoAirUuidUtil.randomUUID8())
                .Channel(channelInfo.getChannelCode())
                .ChannelPwd(channelInfo.getChannelPwd())
                .Header(header)
                .build();
        memberLoginService.Logout(ptApiCRMRequest);
    }

}
