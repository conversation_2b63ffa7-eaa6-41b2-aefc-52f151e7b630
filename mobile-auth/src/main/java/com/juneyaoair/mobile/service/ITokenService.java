package com.juneyaoair.mobile.service;

import com.juneyaoair.mobile.dto.LoginResult;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/2 13:14
 */
public interface ITokenService {

    /**
     * 创建会员登录凭据
     * @param channelCode 登录渠道
     * @param ffpId 会员ID
     * @param ffpNo 会员卡号
     * @param timeout 登录凭证有效期
     * @return
     */
    LoginResult createToken(String channelCode,String ffpId,String ffpNo,Long timeout);
}
