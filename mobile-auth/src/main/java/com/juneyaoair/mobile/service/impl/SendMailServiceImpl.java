package com.juneyaoair.mobile.service.impl;

import com.juneyaoair.mobile.exception.ServiceException;
import com.juneyaoair.mobile.service.SendMailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR>
 * @Description 发送邮件服务
 * @Date 8:30 2023/8/25
 **/
@Slf4j
@Service
public class SendMailServiceImpl implements SendMailService {

    @Value("${spring.mail.host:mail.juneyaoair.com}")
    private String host;
    @Value("${spring.mail.username:<EMAIL>}")
    private String username;
    @Value("${spring.mail.password:FdZnPtXx8OpL}")
    private String password;

    /**
     * @param subject     主题
     * @param from        发件人
     * @param fileName    添加附件时的附件名
     * @param toMail      收件人,字符串格式,以英文逗号分隔
     * @param content     正文内容
     * @param inputStream 附件内容
     * @param ccList      抄送人,字符串格式,以英文逗号分隔
     * @return boolean
     * <AUTHOR>
     * @Description 发送邮件(带附件)
     * @Date 9:38 2023/8/25
     **/
    @Override
    public boolean toSendEmail(String from, String subject, String fileName, String toMail, String content, InputStream inputStream, String ccList) {
        try {
            Properties props = new Properties();
            props.put("mail.smtp.host", host);   // 指定SMTP服务器
            props.put("mail.smtp.auth", "true"); // 指定是否需要SMTP验证
            Session session = Session.getInstance(props, null);
            MimeMessage message = new MimeMessage(session);
            //指定发送人
            message.setFrom(new InternetAddress(username));
            //指定收件人人(多个收件人用,隔开)
            List<Address> addressList = new ArrayList<>();
            String[] split = toMail.split(",");
            for (String s : split) {
                addressList.add(new InternetAddress(s));
            }
            message.addRecipients(Message.RecipientType.TO, addressList.toArray(new Address[0]));
            //指定抄送人
            if (StringUtils.isNotEmpty(ccList)) {
                message.addRecipients(Message.RecipientType.CC, ccList);
            }
            //设置标题
            message.setSubject(subject);
            message.addHeader("charset", "UTF-8");

            /*添加正文内容*/
            //一个Multipart对象包含一个或多个bodyPart对象，组成邮件正文
            Multipart multipart = new MimeMultipart();

            if (StringUtils.isNotEmpty(content)) {
                MimeBodyPart contentPart = new MimeBodyPart();
                contentPart.setText(content, "UTF-8");
                contentPart.setHeader("Content-Type", "text/html; charset=UTF-8");
                multipart.addBodyPart(contentPart);
            }

            /*添加附件*/
            if (StringUtils.isNotEmpty(fileName) && null != inputStream) {
                MimeBodyPart fileBody = new MimeBodyPart();
                DataSource source = new ByteArrayDataSource(inputStream, "application/msexcel");
                fileBody.setDataHandler(new DataHandler(source));
                fileBody.setFileName(MimeUtility.encodeText(fileName));
                multipart.addBodyPart(fileBody);
            }

            message.setContent(multipart);
            message.setSentDate(new Date());
            message.saveChanges();
            Transport transport = session.getTransport("smtp");
            transport.connect(host, username, password);
            transport.sendMessage(message, message.getAllRecipients());
            transport.close();
            return true;
        } catch (Exception e) {
            log.error("邮件发送异常:", e);
            throw new ServiceException("邮件发送异常");
        }
    }

}