package com.juneyaoair.mobile;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */

@EnableApolloConfig(value = {"application","auth"})
@EnableDiscoveryClient
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class},scanBasePackages = "com.juneyaoair")
@EnableFeignClients(basePackages = "com.juneyaoair")
public class MobileAuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(MobileAuthApplication.class, args);
    }

}
