package com.juneyaoair.mobile.controller;

import com.juneyaoair.mobile.dto.Captcha;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.service.LoginService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;
import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @description 验证码服务
 * @date 2018/7/20  9:17.
 */
@RestController
@Api(value = "CaptchaController", tags = "验证码服务")
public class CaptchaController extends BaseController {
    @Autowired
    private LoginService loginService;

    @ApiOperation(value = "发送验证码", notes = "发送验证码")
    @PostMapping(value = "/send", produces = {"application/json;charset=UTF-8"})
    public ResponseData sendCaptcha(@RequestBody RequestDataDto<Captcha> requestData, HttpServletRequest request) {
        //暂时注销此方法
/*        Captcha captcha = requestData.getData();
        if(ObjectUtils.isEmpty(captcha)){
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        BizDto bizDto = initBizDto(request);
        loginService.sendCaptcha(requestData.getChannelNo(),bizDto,captcha);*/
        return ResponseData.suc();
    }
}
