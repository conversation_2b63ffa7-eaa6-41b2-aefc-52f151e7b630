package com.juneyaoair.mobile.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.mobile.service.LoginService;
import com.juneyaoair.oneorder.common.dto.BizDto;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;import com.juneyaoair.oneorder.controller.BaseController;
import com.juneyaoair.oneorder.restresult.response.RequestData;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description 登录服务控制接口
 * @date 2023/5/31 10:17
 */
@Slf4j
@Api(value = "TokenController",tags = "登录控制服务")
@RestController
@RequestMapping
public class TokenController extends BaseController {
    @Autowired
    private LoginService loginService;
    @ApiOperation(value = "退出登录",notes = "退出登录")
    @PostMapping("/loginout")
    public ResponseData loginout(@RequestBody @Validated RequestDataDto requestData, BindingResult bindingResult, HttpServletRequest request, HttpServletResponse response){
        checkBaseParam(bindingResult);
        BizDto bizDto = initBizDto(request);
        //因为前端获取不到对应的cookie，此处自行从cookie获取
        Cookie[] cookies =  request.getCookies();
        if(cookies !=null){
            String uscka = "";
            for(Cookie cookie:cookies){
                if(".uscka".equals(cookie.getName())){
                    uscka = cookie.getValue();
                    break;
                }
            }
            log.info("当前获取到的uscka:{}",uscka);
            if(StringUtils.isNotBlank(uscka) && StringUtils.isNotBlank(requestData.getFfpId())){
                loginService.loginout(bizDto,requestData.getFfpId(),uscka);
                Cookie newCookie=new Cookie(".uscka",null);
                newCookie.setMaxAge(0); //立即删除型
                //newCookie.setDomain(domain);//保存cookie的IP地址,则是删除这个IP的cookie
                newCookie.setPath("/"); //项目所有目录均有效，这句很关键，否则不敢保证删除
                newCookie.setHttpOnly(true);
                response.addCookie(newCookie); //重新写入，将覆盖之前的
            }
        }
        // 会话注销，根据账号id 和 设备类型
        StpUtil.logout(requestData.getFfpNo(),requestData.getChannelNo());
        return ResponseData.suc();
    }
}
