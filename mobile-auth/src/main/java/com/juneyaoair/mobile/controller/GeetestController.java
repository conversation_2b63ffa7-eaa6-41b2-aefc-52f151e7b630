package com.juneyaoair.mobile.controller;

import com.juneyaoair.flightbasic.utils.IpUtils;
import com.juneyaoair.mobile.exception.ArgumentCheckFailException;
import com.juneyaoair.mobile.exception.dto.ResponseData;
import com.juneyaoair.oneorder.api.geetest.sdk.dto.GeetestLibResult;
import com.juneyaoair.oneorder.api.geetest.sdk.enums.DigestmodEnum;
import com.juneyaoair.oneorder.api.geetest.service.IGeetestService;
import com.juneyaoair.oneorder.common.dto.RequestDataDto;import com.juneyaoair.oneorder.geetest.dto.GeetestClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/16 11:12
 */
@RestController
@Api(value = "GeetestController", tags = "极验服务")
public class GeetestController {
    @Autowired
    private IGeetestService geetestService;

    @ApiOperation(value = "极验初始化服务", notes = "极验初始化服务")
    @PostMapping(value = "/initGeetest", produces = {"application/json;charset=UTF-8"})
    public ResponseData<GeetestLibResult> initGeetest(@RequestBody @Valid RequestDataDto<GeetestClient> requestData, HttpServletRequest request){
        GeetestClient geetestClient = requestData.getData();
        if(ObjectUtils.isEmpty(geetestClient)){
            throw new ArgumentCheckFailException("业务参数不可为空");
        }
        DigestmodEnum digestmodEnum = DigestmodEnum.MD5;
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("digestmod", digestmodEnum.getName());
        paramMap.put("user_id", IpUtils.getIpAddr(request));
        paramMap.put("client_type", geetestClient.getClient_type());
        paramMap.put("ip_address", IpUtils.getIpAddr(request));
        GeetestLibResult result =geetestService.register(geetestClient.getScene(),digestmodEnum,paramMap);
        return ResponseData.suc(result);
    }
}
