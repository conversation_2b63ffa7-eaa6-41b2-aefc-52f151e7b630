package com.juneyaoair.mobile.dto;

import com.juneyaoair.oneorder.constant.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @description
 * @date 2023/6/16 9:12
 */
@Data
@ApiModel(value = "LoginCaptchaBody",description = "验证码登录请求体")
public class LoginCaptchaBody {
    @ApiModelProperty(value = "国家区号代码",required = true)
    @NotBlank(message = "国家区号代码")
    private String countryCode;
    @ApiModelProperty(value = "手机号",required = true)
    @NotBlank(message = "账号不可为空")
    @Pattern(regexp = PatternCommon.MOBILE_INTERNATIONAL,message = "请输入正确的手机号")
    private String account;
    @ApiModelProperty(value = "验证码",required = true)
    @NotBlank(message = "验证码不可为空")
    private String captcha;

    private String ip;
}
