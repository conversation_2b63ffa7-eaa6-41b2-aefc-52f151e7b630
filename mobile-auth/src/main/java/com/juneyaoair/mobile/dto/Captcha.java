package com.juneyaoair.mobile.dto;

import com.juneyaoair.oneorder.common.dto.GeetestDto;
import com.juneyaoair.oneorder.constant.PatternCommon;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2018/7/20  16:45.
 */
@ApiModel(value = "Captcha",description = "发送验证吗参数")
@Data
public class Captcha extends GeetestDto {
    @ApiModelProperty(value="手机号",required=true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = PatternCommon.MOBILE_INTERNATIONAL,message = "请输入正确的手机号")
    private String mobileNum;
    /**
     * 手机号码国际代码
     */
    @ApiModelProperty(value="国际区号",required=true)
    @NotBlank(message = "国际区号不能为空")
    private String countryCode;
    /**
     * 操作类型
     */
    @ApiModelProperty(value="短信类型",allowableValues = "logincodemobile",required=true)
    @NotBlank(message = "短信类型不能为空")
    private String type;

    @ApiModelProperty(value="同盾设备指纹",required=true)
    private String blackBox;
}
