package com.juneyaoair.mobile.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2023/5/31 10:34
 */
@Data
@ApiModel(value = "LoginResult",description = "登录结果DTO")
public class LoginResult {
    @ApiModelProperty(value = "登录凭据")
    private String accessToken;
    @ApiModelProperty(value = "会员卡号")
    private String ffpNo;
    @ApiModelProperty(value = "凭据过期时间")
    private long expiresIn;
    @ApiModelProperty(value = "登录版本，用户后期切换时采用不同的验证方式")
    private String loginVersion;
}
