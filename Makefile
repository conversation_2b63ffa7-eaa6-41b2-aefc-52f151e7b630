all: c b p
b: accountbff bookbff cuss mainpage auth sso searchbff orderbff gateway hocargateway hocarauth2b hocargateway2b
p: paccountbff pbookbff pcuss pmainpage pauth psso psearchbff porderbff pgateway phocargateway phocarauth2b phocargateway2b
#         ---  tag 命名规范   ---
# 测试环境  默认测试版本号 alpha 分支版本测试   1.0.1-alpha
# 预发布环境 默认测试版本号 beta 分支版本测试   1.0.1-beta
# 生产环境 默认版本号 latest 分支版本   1.0.1 或 1.0.1-release
TARGET=harbor.hoair.cn/mobile
TAG=
ifdef tag
	TAG = $(tag)
else
	TAG = alpha
endif
ENV=
ifdef env
	ENV = $(env)
else
	ENV = test
endif


c:
	mvn clean
	mvn package -Dmaven.test.skip=true -P$(ENV)
accountbff:
	cd mobile-service/accountbff-service && docker build  -t $(TARGET)/accountbff:$(TAG) .
paccountbff:
	docker push $(TARGET)/accountbff:$(TAG)
bookbff:
	cd mobile-service/bookbff-service && docker build  -t $(TARGET)/bookbff:$(TAG) .
pbookbff:
	docker push $(TARGET)/bookbff:$(TAG)
cuss:
	cd mobile-service/cuss-service && docker build  -t $(TARGET)/cuss:$(TAG) .
pcuss:
	docker push $(TARGET)/cuss:$(TAG)
mainpage:
	cd mobile-service/mainpage-service && docker build  -t $(TARGET)/mainpage:$(TAG) .
pmainpage:
	docker push $(TARGET)/mainpage:$(TAG)
auth:
	cd mobile-auth && docker build  -t $(TARGET)/auth:$(TAG) .
pauth:
	docker push $(TARGET)/auth:$(TAG)
sso:
	cd mobile-sso && docker build  -t $(TARGET)/sso:$(TAG) .
psso:
	docker push $(TARGET)/sso:$(TAG)
orderbff:
	cd mobile-service/orderbff-service && docker build  -t $(TARGET)/orderbff:$(TAG) .
porderbff:
	docker push $(TARGET)/orderbff:$(TAG)
searchbff:
	cd mobile-service/searchbff-service && docker build  -t $(TARGET)/searchbff:$(TAG) .
psearchbff:
	docker push $(TARGET)/searchbff:$(TAG)
gateway:
	cd mobile-gateway && docker build  -t $(TARGET)/gateway:$(TAG) .
pgateway:
	docker push $(TARGET)/gateway:$(TAG)
hocargateway:
	cd mobile-hocar-gateway && docker build  -t $(TARGET)/hocargateway:$(TAG) .
phocargateway:
	docker push $(TARGET)/hocargateway:$(TAG)
hocarauth2b:
	cd hocar-auth2b && docker build  -t $(TARGET)/hocarauth2b:$(TAG) .
phocarauth2b:
	docker push $(TARGET)/hocarauth2b:$(TAG)
hocargateway2b:
	cd hocar-gateway2b && docker build  -t $(TARGET)/hocargateway2b:$(TAG) .
phocargateway2b:
	docker push $(TARGET)/hocargateway2b:$(TAG)

#run:
#   docker run --name=ho-gateway -v /opt/log/:/opt/log/ --restart=always -e "spring.profiles.active=uat" -e "server.port=9031" -e "show.api.disabled=false" -e "server.ssl.enabled=false" -e "oauth.services.ignore=/**"  --network=host -d harbor.hoair.cn/horder/gateway:beta
#   docker run --name=ho-gateway -v /opt/log/:/opt/log/ --restart=always -e "spring.profiles.active=uat" -e "server.port=9032" -e "show.api.disabled=true" -e "server.ssl.enabled=true"    --network=host -d harbor.hoair.cn/horder/gateway:beta
#	docker run --name=horder-gateway -v D:/data/log/:/data/log/ --restart=always -e "server.port=9031" -e "show.api.disabled=false" -e "oauth.services.ignore=/**" --network=host -d harbor.hoair.cn/horder/gateway:alpha
#	docker run --name=horder-gateway -v D:/data/log/:/data/log/ --restart=always -e "server.port=9032" -e "show.api.disabled=true" --network=host -d harbor.hoair.cn/horder/gateway:latest
#	docker run --name=horder-gateway -v D:/data/log/:/data/log/ --restart=always -e "server.port=9033" -e "show.api.disabled=true" --network=host -d harbor.hoair.cn/horder/gateway:alpha
#	docker run --name=horder-coupon -v D:/data/log/:/data/log/ --restart=always  --network=host -d harbor.hoair.cn/horder/coupon
#	docker run --name=horder-order -v D:/data/log/:/data/log/ --restart=always  --network=host -d harbor.hoair.cn/horder/order
#	docker run --name=horder-callcenter -v D:/data/log/:/data/log/ --restart=always  --network=host -d harbor.hoair.cn/horder/callcenter
#	docker run --name=horder-book -v D:/data/log/:/data/log/ --restart=always  --network=host -d harbor.hoair.cn/horder/book
#	docker run --name=horder-refund -v D:/data/log/:/data/log/ --restart=always  --network=host -d harbor.hoair.cn/horder/refund
#	docker run --name=horder-payment -v D:/data/log/:/data/log/ --restart=always  --network=host -d harbor.hoair.cn/horder/payment
#	docker run --name=horder-shop -v D:/data/log/:/data/log/ --restart=always  --network=host -d harbor.hoair.cn/horder/shop


f-biz:
	docker run --restart=always --user=root --network=host --name filebeat-biz -d -v C:/Users/<USER>/Desktop/log/:/var/log/ -v D:/ws-idea/vNextOrderPlatform/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml elastic/filebeat:7.6.0
so:
	mvn sonar:sonar -D sonar.projectKey=ho-mobile-bff -D sonar.host.url=http://sonar.juneyaoair.com:9000  -D sonar.login=sqa_461b9fa6937b3b9358bacffd9b5f785b2dd862c9




